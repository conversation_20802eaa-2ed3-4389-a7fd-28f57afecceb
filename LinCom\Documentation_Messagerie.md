# Documentation du Module de Messagerie LinCom

## Vue d'ensemble

Le module de messagerie de LinCom a été considérablement amélioré pour offrir une expérience utilisateur moderne et complète. Cette documentation décrit les fonctionnalités implémentées et les améliorations apportées.

## Fonctionnalités Principales

### 1. Messagerie Privée
- **Conversations 1-à-1** : Communication directe entre deux membres
- **Création automatique de conversations** : Les conversations sont créées automatiquement lors du premier message
- **Gestion des participants** : Système robuste de gestion des participants aux conversations

### 2. Messagerie de Groupe
- **Support des groupes** : Possibilité d'envoyer des messages à plusieurs participants
- **Gestion des participants de groupe** : Ajout/suppression de membres dans les groupes
- **Messages broadcast** : Diffusion de messages à tous les participants

### 3. Gestion des Fichiers et Pièces Jointes
- **Upload de fichiers** : Support pour images, documents PDF, Word, Excel, PowerPoint
- **Prévisualisation** : Aperçu des fichiers avant envoi
- **Validation** : Vérification du type et de la taille des fichiers (max 10MB)
- **Stockage sécurisé** : Fichiers stockés dans un dossier dédié avec noms uniques

### 4. Interface Utilisateur Améliorée
- **Design moderne** : Interface responsive avec animations CSS
- **Indicateurs visuels** : Messages lus/non lus, indicateur de frappe
- **Émojis** : Picker d'émojis intégré
- **Recherche** : Fonction de recherche dans les messages
- **Notifications** : Système de notifications toast

### 5. Fonctionnalités Avancées
- **Pagination** : Chargement progressif des messages
- **Recherche** : Recherche dans le contenu des messages et noms d'utilisateurs
- **Statuts de lecture** : Suivi des messages lus/non lus
- **Auto-refresh** : Actualisation automatique des messages
- **Responsive design** : Adaptation mobile et tablette

## Architecture Technique

### Classes Principales

#### 1. Message_Class
```csharp
public class Message_Class
{
    public long MessageId { get; set; }
    public long? ConversationId { get; set; }
    public long? SenderId { get; set; }
    public string Contenu { get; set; }
    public string AttachmentUrl { get; set; }
    public DateTime? DateEnvoi { get; set; }
    public string name { get; set; }
}
```

#### 2. Conversation_Class
```csharp
public class Conversation_Class
{
    public long ConversationId { get; set; }
    public string Sujet { get; set; }
    public int? IsGroup { get; set; }
    public DateTime? CreatedAt { get; set; }
}
```

#### 3. FichierMessage_Class (Améliorée)
```csharp
public class FichierMessage_Class
{
    public long FichierId { get; set; }
    public long? MessageId { get; set; }
    public string NomFichier { get; set; }
    public string UrlFichier { get; set; }
    public string TypeFichier { get; set; }
    public long? TailleFichier { get; set; }
    public DateTime? DateUpload { get; set; }
    // ... propriétés calculées
}
```

### Interfaces et Implémentations

#### IMessage (Interface étendue)
- `AjouterMessageEtStatusPourTous()` : Méthode optimisée pour créer message + statuts
- `RechercherMessages()` : Recherche dans les messages
- `ChargerConversationsRecentes()` : Chargement des conversations récentes
- `CompterMessagesNonLusConversation()` : Comptage des messages non lus
- `MarquerConversationCommeLue()` : Marquage en lecture
- `VerifierPermissionMessage()` : Vérification des permissions
- `ChargerMessagesAvecPagination()` : Pagination des messages

#### IFichierMessage (Nouvelle interface)
- `AjouterFichier()` : Ajout de fichiers
- `UploadFichier()` : Upload sécurisé
- `VerifierTypeFichierAutorise()` : Validation des types
- `VerifierTailleFichier()` : Validation de la taille

## Sécurité

### Validation des Données
- **Sanitisation** : Échappement HTML pour prévenir les injections XSS
- **Validation des fichiers** : Types et tailles autorisés
- **Permissions** : Vérification des droits d'accès aux conversations

### Protection des Fichiers
- **Noms uniques** : Génération de noms de fichiers sécurisés
- **Dossier protégé** : Stockage dans un répertoire dédié
- **Validation MIME** : Vérification du type réel des fichiers

## Installation et Configuration

### Prérequis
1. .NET Framework 4.5+
2. Entity Framework 6.x
3. jQuery 3.6+
4. Font Awesome 6.0+

### Fichiers Modifiés/Ajoutés
- `messagerie.aspx` : Interface utilisateur améliorée
- `messagerie.aspx.cs` : Logique métier optimisée
- `MessageImp.cs` : Nouvelles méthodes implémentées
- `FichierMessage_Class.cs` : Classe améliorée
- `IFichierMessage.cs` : Nouvelle interface
- `FichierMessageImp.cs` : Nouvelle implémentation
- `messagerie.js` : Fonctionnalités JavaScript
- `Documentation_Messagerie.md` : Cette documentation

### Configuration Requise
1. Créer le dossier `~/file/messages/` pour le stockage des fichiers
2. Configurer les permissions d'écriture sur ce dossier
3. Vérifier la configuration Entity Framework dans `Web.config`

## Utilisation

### Pour les Développeurs
1. **Envoi de message** : Utiliser `AjouterMessageEtStatusPourTous()` pour une gestion optimisée
2. **Gestion des fichiers** : Utiliser `FichierMessageImp` pour l'upload sécurisé
3. **Recherche** : Implémenter `RechercherMessages()` pour la recherche avancée

### Pour les Utilisateurs
1. **Sélectionner un contact** : Cliquer sur un contact dans la liste
2. **Envoyer un message** : Taper le message et appuyer sur Entrée ou cliquer sur Envoyer
3. **Joindre un fichier** : Cliquer sur l'icône trombone et sélectionner le fichier
4. **Rechercher** : Utiliser la barre de recherche pour trouver des messages
5. **Émojis** : Cliquer sur l'icône smiley pour insérer des émojis

## Améliorations Futures Possibles

1. **Notifications en temps réel** : Intégration SignalR
2. **Messages vocaux** : Support des enregistrements audio
3. **Vidéoconférence** : Intégration d'appels vidéo
4. **Chiffrement** : Chiffrement end-to-end des messages
5. **Archivage** : Système d'archivage des anciennes conversations
6. **Modération** : Outils de modération pour les administrateurs
7. **Traduction** : Traduction automatique des messages
8. **Mentions** : Système de mentions (@utilisateur)
9. **Réactions** : Réactions aux messages (like, dislike, etc.)
10. **Statut en ligne** : Indicateur de présence des utilisateurs

## Support et Maintenance

Pour toute question ou problème concernant le module de messagerie :
1. Vérifier les logs d'erreur dans le serveur
2. Contrôler les permissions de fichiers
3. Vérifier la connectivité à la base de données
4. Consulter cette documentation pour les fonctionnalités

## Changelog

### Version 2.0 (Actuelle)
- ✅ Interface utilisateur modernisée
- ✅ Gestion des fichiers et pièces jointes
- ✅ Système de recherche
- ✅ Indicateurs de lecture
- ✅ Support des émojis
- ✅ Responsive design
- ✅ Optimisation des performances
- ✅ Sécurité renforcée

### Version 1.0 (Précédente)
- Messagerie de base
- Conversations privées
- Interface simple
