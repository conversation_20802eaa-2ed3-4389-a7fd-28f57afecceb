using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.Services;

namespace LinCom
{
    public partial class messagerie : System.Web.UI.Page
    {
        IMessage objmes = new MessageImp();
        Message_Class mess = new Message_Class();
        IConversation objconver = new ConversationImp();
        Conversation_Class conver = new Conversation_Class();
        ParticipantConversation_Class partconver = new ParticipantConversation_Class();
        MessageStatus_Class messtatu = new MessageStatus_Class();

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();
        int info;
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static long conversationreceveur;
        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte

            }
            if (!IsPostBack)
            {
                // Générer un token unique pour cette session de page
                ViewState["PageToken"] = Guid.NewGuid().ToString();

                // Initialiser les champs cachés
                hdnCurrentUserId.Value = ide.ToString();
                hdnConversationId.Value = "0";
                hdnIsGroup.Value = "0";

                // Vérifier et créer le dossier de messages
                VerifierDossierMessages();

                ChargerMessages();
                AppelMethode();
            }
        }

        private void VerifierDossierMessages()
        {
            try
            {
                string dossier = Server.MapPath("~/file/messages/");
                if (!System.IO.Directory.Exists(dossier))
                {
                    System.IO.Directory.CreateDirectory(dossier);

                    // Créer un fichier .htaccess pour sécuriser le dossier (optionnel)
                    string htaccessPath = System.IO.Path.Combine(dossier, ".htaccess");
                    if (!System.IO.File.Exists(htaccessPath))
                    {
                        System.IO.File.WriteAllText(htaccessPath, "# Protection du dossier messages\n# Autoriser seulement certains types de fichiers\n");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log l'erreur mais ne pas interrompre le chargement de la page
                System.Diagnostics.Debug.WriteLine($"Erreur création dossier messages: {ex.Message}");
            }
        }
        public void AppelMethode()
        {
            objmem.ChargerListview(listmembre,-1,"actif","");

          
        }
     

        protected void listmembre_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "viewmem" || e.CommandName == "SelectContact")
            {
                long idMembre = Convert.ToInt64(e.CommandArgument.ToString());
                // Utilisez l'ID pour récupérer les détails du membre
                objmem.AfficherDetails(idMembre, mem);

                // Changez le titre de la discussion
                lblHeader.Text = "Chat avec " + mem.Nom + " " + mem.Prenom;
                lblId.Text = mem.MembreId.ToString();

                ChargerMessages();
            }
        }

    private void CreationConversation(int cd,string sujetgroup)
        {//creation d'une nouvelle convrsation

            if (cd==0)
            {//privee
                conver.Sujet = "";
                conver.IsGroup = 0;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
            else if (cd==1)
            {
                //equipe
                conver.Sujet = sujetgroup;
                conver.IsGroup = 1;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
              
        }
      private  void CreationParticipantConversation(long memparticipant)
        {//creation des membres qui commencent le tchat
         //long conversationreceveur = objconver.VerifierConversationId(ide, Convert.ToInt64(memparticipant));
         //if (conversationreceveur > 0)
         //{
         //    partconver.ConversationId = conversationreceveur;
         //    partconver.MembreId= memparticipant;
         //    partconver.JoinedAt = DateTime.Now;

            //    objconver.AjouterParticipant(conversationreceveur, Convert.ToInt64(memparticipant));

            //}
            conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);

            // Si aucune conversation => on la crée
            if (conversationreceveur <= 0)
            {
                CreationConversation(0, ""); // conversation privée
                conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);
            }
            else
            {
                partconver.ConversationId = conversationreceveur;
                partconver.MembreId = memparticipant;
                partconver.JoinedAt = DateTime.Now;
                // Ensuite on ajoute les 2 participants S'ILS NE SONT PAS DÉJÀ DEDANS

                if (!objconver.ParticipantExiste(conversationreceveur, ide))
                    objconver.AjouterParticipant(conversationreceveur, ide);

                if (!objconver.ParticipantExiste(conversationreceveur, memparticipant))
                    objconver.AjouterParticipant(conversationreceveur, memparticipant);

            }

        }
        private int CreationMessage(long convID,long membrId)
        {
            mess.ConversationId = convID;
            mess.SenderId = membrId;
            mess.Contenu = txtMessage.Value;
            mess.DateEnvoi = DateTime.Now;
            mess.name = "";
            mess.AttachmentUrl = "";
            info=objmes.Envoyer(mess);

            return info;

        }
        private int CreationMessagestatus(long convID, long membrId,int lire)
        {
            messtatu.MessageId = convID;
            messtatu.UserId = membrId;
            messtatu.IsRead = lire;
            messtatu.ReadAt = DateTime.Now;
           
            info =objmes.EnvoyerMessageStatus(messtatu);

            return info;
        }

        private void EnvoieMessagerie()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                string contenuMessage = txtMessage.Value.Trim();

                // Vérifier les doublons récents
                if (VerifierDoublonRecent(contenuMessage))
                {
                    Response.Write("<script>alert('Ce message a déjà été envoyé récemment.');</script>");
                    return;
                }

                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";
                int info = 0;

                if (isGroup)
                {
                    // Gestion des messages de groupe - implémentation complète
                    long idGroupe = destinataireId;
                    info = objmes.AjouterMessageEtStatusPourTous(idGroupe, senderId, contenuMessage);
                }
                else
                {
                    // Tchat privé - utilisation de la méthode optimisée
                    long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    if (conversationId > 0)
                    {
                        info = objmes.AjouterMessageEtStatusPourTous(conversationId, senderId, contenuMessage);
                    }
                    else
                    {
                        info = 0; // Erreur de conversation
                    }
                }

                // Recharger les messages seulement si l'envoi a réussi
                if (info == 1)
                {
                    ChargerMessages();
                    Response.Write("<script>showSuccessMessage('Message envoyé avec succès!');</script>");
                }

                if (info != 1)
                    Response.Write("<script>alert('Erreur lors de l’envoi du message.');</script>");
            }
        }
        protected void btnenvoie_ServerClick(object sender, EventArgs e)
        {
            // Vérifier que le message n'est pas vide
            if (string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                Response.Write("<script>alert('Veuillez saisir un message.');</script>");
                return;
            }

            // Créer un hash unique pour ce message
            string messageContent = txtMessage.Value.Trim();
            string currentHash = messageContent.GetHashCode().ToString() + "_" + DateTime.Now.Ticks;

            // Vérifier si c'est une re-soumission
            if (hdnLastMessageHash.Value == messageContent.GetHashCode().ToString())
            {
                // Même message, vérifier si c'est récent
                if (Session["LastMessageTime"] != null)
                {
                    DateTime lastTime = (DateTime)Session["LastMessageTime"];
                    if ((DateTime.Now - lastTime).TotalSeconds < 3)
                    {
                        txtMessage.Value = "";
                        Response.Write("<script>alert('Message déjà envoyé récemment!');</script>");
                        return;
                    }
                }
            }

            // Enregistrer l'heure et le hash du message
            Session["LastMessageTime"] = DateTime.Now;
            hdnLastMessageHash.Value = messageContent.GetHashCode().ToString();

            // Envoyer le message
            EnvoieMessagerie();

            // Vider le champ de texte après envoi
            txtMessage.Value = "";

            // Générer un nouveau token pour éviter les re-soumissions
            ViewState["PageToken"] = Guid.NewGuid().ToString();
        }
        void ChargerMessages()
        {
            //chargement des messages
            long senderId = ide;
            long destinataireId = Convert.ToInt64(lblId.Text);
            long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

            objmes.ChargerMessages(rptMessages, conversationId, 1000);

            // Marquer les messages comme lus
            objmes.MarquerConversationCommeLue(conversationId, senderId);
        }

        // Méthodes WebMethod pour les appels AJAX
        [WebMethod]
        public static string RechercherMessages(long membreId, string motCle, long conversationId)
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var query = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.Contenu.Contains(motCle)
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   DateEnvoi = m.DateEnvoi,
                                   ConversationId = m.ConversationId
                               };

                    // Si une conversation spécifique est sélectionnée, filtrer par conversation
                    if (conversationId > 0)
                    {
                        query = query.Where(m => m.ConversationId == conversationId);
                    }
                    else
                    {
                        // Sinon, filtrer par les conversations où l'utilisateur participe
                        var conversationsUtilisateur = con.ParticipantConversations
                            .Where(pc => pc.MembreId == membreId)
                            .Select(pc => pc.ConversationId);

                        query = query.Where(m => conversationsUtilisateur.Contains(m.ConversationId));
                    }

                    var resultats = query
                        .OrderByDescending(m => m.DateEnvoi)
                        .Take(50)
                        .ToList();

                    // Convertir en JSON
                    var json = Newtonsoft.Json.JsonConvert.SerializeObject(resultats);
                    return json;
                }
            }
            catch (Exception ex)
            {
                return "[]"; // Retourner un tableau vide en cas d'erreur
            }
        }

        [WebMethod]
        public static string MarquerCommeLue(long conversationId, long membreId)
        {
            try
            {
                IMessage objmes = new MessageImp();
                objmes.MarquerConversationCommeLue(conversationId, membreId);
                return "Success";
            }
            catch (Exception ex)
            {
                return "Erreur: " + ex.Message;
            }
        }

        [WebMethod]
        public static int CompterMessagesNonLus(long membreId)
        {
            try
            {
                IMessage objmes = new MessageImp();
                return objmes.CompterNonLus(membreId);
            }
            catch
            {
                return 0;
            }
        }

        // Méthode pour vérifier les doublons récents
        private bool VerifierDoublonRecent(string contenu)
        {
            try
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);

                // Vérifier s'il y a un message identique dans les 30 dernières secondes
                long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                using (Connection con = new Connection())
                {
                    DateTime limiteTemps = DateTime.Now.AddSeconds(-30);

                    var messageRecent = con.Messages
                        .Where(m => m.ConversationId == conversationId &&
                               m.SenderId == senderId &&
                               m.Contenu == contenu &&
                               m.DateEnvoi >= limiteTemps)
                        .FirstOrDefault();

                    return messageRecent != null;
                }
            }
            catch
            {
                return false; // En cas d'erreur, on autorise l'envoi
            }
        }

        // Méthode pour gérer l'envoi avec fichier
        protected void btnEnvoyerAvecFichier_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier qu'un fichier est sélectionné
                if (!fileUpload.HasFile)
                {
                    Response.Write("<script>alert('Aucun fichier sélectionné.');</script>");
                    return;
                }

                // Vérifier la taille du fichier (10 MB max)
                if (fileUpload.PostedFile.ContentLength > 10 * 1024 * 1024)
                {
                    Response.Write("<script>alert('Le fichier est trop volumineux (max 10 MB).');</script>");
                    return;
                }

                // Vérifier le type de fichier
                string[] typesAutorises = { ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".zip", ".rar" };
                string extension = System.IO.Path.GetExtension(fileUpload.FileName).ToLower();

                if (!typesAutorises.Contains(extension))
                {
                    Response.Write("<script>alert('Type de fichier non autorisé.');</script>");
                    return;
                }

                // Créer le dossier s'il n'existe pas
                string dossier = Server.MapPath("~/file/messages/");
                if (!System.IO.Directory.Exists(dossier))
                {
                    System.IO.Directory.CreateDirectory(dossier);
                }

                // Générer un nom unique pour le fichier
                string nomUnique = $"{ide}_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid().ToString("N").Substring(0, 8)}{extension}";
                string cheminComplet = System.IO.Path.Combine(dossier, nomUnique);

                // Sauvegarder le fichier
                fileUpload.SaveAs(cheminComplet);

                // URL relative du fichier (sans ~/)
                string urlFichier = $"file/messages/{nomUnique}";

                // Vérifier que le fichier a bien été sauvegardé
                if (System.IO.File.Exists(cheminComplet))
                {
                    // Envoyer le message avec le fichier
                    EnvoyerMessageAvecFichier(urlFichier, fileUpload.FileName);
                    Response.Write("<script>alert('Message avec fichier envoyé avec succès!'); resetFileUpload();</script>");
                }
                else
                {
                    Response.Write("<script>alert('Erreur lors de la sauvegarde du fichier.');</script>");
                }
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur lors de l'upload: {ex.Message}');</script>");
            }
        }

        private void EnvoyerMessageAvecFichier(string urlFichier, string nomFichier)
        {
            try
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";

                // Créer le message avec le fichier
                string contenuMessage = string.IsNullOrWhiteSpace(txtMessage.Value) ?
                    $"📎 Fichier partagé: {nomFichier}" : txtMessage.Value.Trim();

                long conversationId;
                if (isGroup)
                {
                    conversationId = destinataireId; // Pour les groupes
                }
                else
                {
                    conversationId = objconver.VerifierConversationId(senderId, destinataireId);
                }

                if (conversationId > 0)
                {
                    // Envoyer le message avec l'URL du fichier
                    int result = objmes.AjouterMessageEtStatusPourTous(conversationId, senderId, contenuMessage, urlFichier);

                    if (result == 1)
                    {
                        // Ajouter l'entrée dans la table FichierMessage
                        AjouterFichierMessage(conversationId, nomFichier, urlFichier);

                        // Recharger les messages
                        ChargerMessages();

                        // Vider les champs
                        txtMessage.Value = "";
                    }
                    else
                    {
                        Response.Write("<script>alert('Erreur lors de l'envoi du message.');</script>");
                    }
                }
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur: {ex.Message}');</script>");
            }
        }

        private void AjouterFichierMessage(long conversationId, string nomFichier, string urlFichier)
        {
            try
            {
                // Obtenir le dernier message envoyé pour cette conversation
                using (Connection con = new Connection())
                {
                    var dernierMessage = con.Messages
                        .Where(m => m.ConversationId == conversationId && m.SenderId == ide)
                        .OrderByDescending(m => m.DateEnvoi)
                        .FirstOrDefault();

                    if (dernierMessage != null)
                    {
                        // Ajouter l'entrée dans FichierMessage
                        var fichierMessage = new FichierMessage
                        {
                            MessageId = (int)dernierMessage.MessageId,
                            NomFichier = nomFichier,
                            UrlFichier = urlFichier,
                            name = nomFichier
                        };

                        con.FichierMessages.Add(fichierMessage);
                        con.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log l'erreur mais ne pas interrompre le processus
                System.Diagnostics.Debug.WriteLine($"Erreur ajout fichier: {ex.Message}");
            }
        }



        // Méthode pour envoyer un fichier
        protected void btnSendFile_ServerClick(object sender, EventArgs e)
        {
            try
            {
                // Pour une vraie implémentation, vous devriez gérer l'upload du fichier ici
                // En attendant, on simule l'envoi d'un message avec fichier
                EnvoieMessageAvecFichier("", "fichier_test.txt");
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur upload: {ex.Message}');</script>");
            }
        }

        private void EnvoieMessageAvecFichier(string urlFichier, string nomFichier)
        {
            try
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                if (conversationId > 0)
                {
                    // Créer le message avec fichier
                    mess.ConversationId = conversationId;
                    mess.SenderId = senderId;
                    mess.Contenu = !string.IsNullOrEmpty(txtMessage.Value) ? txtMessage.Value.Trim() : "Fichier joint";
                    mess.DateEnvoi = DateTime.Now;
                    mess.AttachmentUrl = urlFichier;
                    mess.name = nomFichier;

                    int result = objmes.Envoyer(mess);

                    if (result == 1)
                    {
                        ChargerMessages();
                        txtMessage.Value = "";
                        Response.Write("<script>removeFile(); alert('Message avec fichier envoyé!');</script>");
                    }
                    else
                    {
                        Response.Write("<script>alert('Erreur lors de l'envoi du fichier.');</script>");
                    }
                }
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur: {ex.Message}');</script>");
            }
        }
    }
}