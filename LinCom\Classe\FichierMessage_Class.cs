using System;

namespace LinCom.Class
{
    public class FichierMessage_Class
    {
        public long FichierId { get; set; }
        public long? MessageId { get; set; }
        public string NomFichier { get; set; }
        public string UrlFichier { get; set; }
        public string Name { get; set; }
        public string TypeFichier { get; set; }
        public long? TailleFichier { get; set; }
        public DateTime? DateUpload { get; set; }
        public long? UploadePar { get; set; }
        public string Description { get; set; }
        public int? Statut { get; set; } // 1 = actif, 0 = supprimé

        // Propriétés calculées
        public string TailleFormatee
        {
            get
            {
                if (!TailleFichier.HasValue) return "Inconnue";

                var taille = TailleFichier.Value;
                if (taille < 1024) return $"{taille} B";
                if (taille < 1024 * 1024) return $"{taille / 1024:F1} KB";
                if (taille < 1024 * 1024 * 1024) return $"{taille / (1024 * 1024):F1} MB";
                return $"{taille / (1024 * 1024 * 1024):F1} GB";
            }
        }

        public bool EstImage
        {
            get
            {
                if (string.IsNullOrEmpty(TypeFichier)) return false;
                var type = TypeFichier.ToLower();
                return type.Contains("image") || type.EndsWith("jpg") || type.EndsWith("jpeg") ||
                       type.EndsWith("png") || type.EndsWith("gif") || type.EndsWith("bmp");
            }
        }
    }
}
