<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TestMessagerie.aspx.cs" Inherits="LinCom.TestMessagerie" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test Module Messagerie</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>Test du Module de Messagerie LinCom</h1>
        
        <div class="test-section info">
            <h3>🔧 Tests de Fonctionnalité</h3>
            <asp:Button ID="btnTestConnexion" runat="server" Text="Test Connexion DB" OnClick="btnTestConnexion_Click" />
            <asp:Button ID="btnTestMessage" runat="server" Text="Test Envoi Message" OnClick="btnTestMessage_Click" />
            <asp:Button ID="btnTestFichier" runat="server" Text="Test Gestion Fichier" OnClick="btnTestFichier_Click" />
        </div>
        
        <div class="test-section">
            <h3>📊 Résultats des Tests</h3>
            <asp:Literal ID="litResultats" runat="server"></asp:Literal>
        </div>
        
        <div class="test-section">
            <h3>📋 Informations Système</h3>
            <asp:Literal ID="litInfos" runat="server"></asp:Literal>
        </div>
    </form>
</body>
</html>
