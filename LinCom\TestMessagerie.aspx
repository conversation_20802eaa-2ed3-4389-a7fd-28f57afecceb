<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TestMessagerie.aspx.cs" Inherits="LinCom.TestMessagerie" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Test Messagerie LinCom</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .status-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
        .status-item { padding: 10px; border-radius: 5px; text-align: center; }
        .status-ok { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>🧪 Test Complet - Messagerie LinCom</h1>
            
            <div class="test-section info">
                <h3>📋 Diagnostic Automatique</h3>
                <p>Cette page teste automatiquement tous les composants de la messagerie.</p>
                <asp:Button ID="btnRunTests" runat="server" Text="🚀 Lancer les Tests" 
                           CssClass="btn btn-primary" OnClick="btnRunTests_Click" />
                <asp:Button ID="btnGoToMessagerie" runat="server" Text="💬 Aller à la Messagerie" 
                           CssClass="btn btn-success" OnClick="btnGoToMessagerie_Click" />
            </div>
            
            <div class="test-section">
                <h3>🔍 État des Composants</h3>
                <div class="status-grid">
                    <div id="authStatus" class="status-item status-pending">
                        <strong>🔐 Authentification</strong><br>
                        <asp:Label ID="lblAuthStatus" runat="server" Text="En attente..."></asp:Label>
                    </div>
                    <div id="dbStatus" class="status-item status-pending">
                        <strong>🗄️ Base de Données</strong><br>
                        <asp:Label ID="lblDbStatus" runat="server" Text="En attente..."></asp:Label>
                    </div>
                    <div id="contactsStatus" class="status-item status-pending">
                        <strong>👥 Contacts</strong><br>
                        <asp:Label ID="lblContactsStatus" runat="server" Text="En attente..."></asp:Label>
                    </div>
                    <div id="messagesStatus" class="status-item status-pending">
                        <strong>💬 Messages</strong><br>
                        <asp:Label ID="lblMessagesStatus" runat="server" Text="En attente..."></asp:Label>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📊 Résultats des Tests</h3>
                <asp:Label ID="lblTestResults" runat="server" Text="Cliquez sur 'Lancer les Tests' pour commencer..."></asp:Label>
            </div>
            
            <div class="test-section">
                <h3>🔧 Actions de Dépannage</h3>
                <asp:Button ID="btnCreateTestUser" runat="server" Text="👤 Créer Utilisateur Test" 
                           CssClass="btn btn-warning" OnClick="btnCreateTestUser_Click" />
                <asp:Button ID="btnTestDatabase" runat="server" Text="🗄️ Tester Base de Données" 
                           CssClass="btn btn-warning" OnClick="btnTestDatabase_Click" />
                <asp:Button ID="btnClearCache" runat="server" Text="🗑️ Vider Cache" 
                           CssClass="btn btn-danger" OnClick="btnClearCache_Click" />
            </div>
            
            <div class="test-section">
                <h3>📝 Log Détaillé</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                    <asp:Label ID="lblDetailedLog" runat="server" Text="Prêt pour les tests..."></asp:Label>
                </div>
            </div>
            
            <div class="test-section success" style="display: none;" id="successSection">
                <h3>✅ Tests Réussis !</h3>
                <p>Tous les composants fonctionnent correctement. Vous pouvez maintenant utiliser la messagerie.</p>
                <a href="messagerie.aspx" class="btn btn-success">🚀 Accéder à la Messagerie</a>
            </div>
        </div>
    </form>
    
    <script>
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = 'status-item status-' + status;
            if (status === 'ok') {
                element.innerHTML = element.innerHTML.replace('En attente...', '✅ ' + message);
            } else if (status === 'error') {
                element.innerHTML = element.innerHTML.replace('En attente...', '❌ ' + message);
            }
        }
        
        function showSuccess() {
            document.getElementById('successSection').style.display = 'block';
        }
    </script>
</body>
</html>
