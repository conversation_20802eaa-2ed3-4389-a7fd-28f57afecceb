﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MessageImp : IMessage
    {
        int msg;
        private Message message = new Message();
        private MessageStatu mesast= new MessageStatu();

        public void AfficherDetails(long messageId, Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    messageClass.MessageId = m.MessageId;
                    messageClass.ConversationId = m.ConversationId;
                    messageClass.SenderId = m.SenderId;
                    messageClass.Contenu = m.Contenu;
                    messageClass.AttachmentUrl = m.AttachmentUrl;
                    messageClass.DateEnvoi = m.DateEnvoi;
                    messageClass.name = m.name;

    }
            }
        }

      

        public void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages)
        {
            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? ""

                               };

                rpt.DataSource = messages.Take(nombreMessages).ToList();
                rpt.DataBind();
            }
        }

        public int CompterNonLus(long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.Messages
                    .Count(m => m.SenderId != membreId &&
                               con.ParticipantConversations.Any(p =>
                                   p.ConversationId == m.ConversationId &&
                                   p.MembreId == membreId));
            }
        }

        public int Envoyer(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                message.ConversationId = messageClass.ConversationId;
                message.SenderId = messageClass.SenderId;
                message.Contenu = messageClass.Contenu;
                message.DateEnvoi = DateTime.Now;
                message.name = messageClass.name;
                message.AttachmentUrl=messageClass.AttachmentUrl;

                try
                {
                    con.Messages.Add(message);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public int EnvoyerMessageStatus(MessageStatus_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                mesast.MessageId = messageClass.MessageId;
                mesast.UserId = messageClass.UserId;
                mesast.IsRead = messageClass.IsRead;
                mesast.ReadAt = DateTime.Now;
              
                try
                {
                    con.MessageStatus.Add(mesast);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }



        public int Modifier(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageClass.MessageId);
                if (m != null)
                {
                    m.Contenu = messageClass.Contenu;
                    m.name = messageClass.name;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long messageId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    con.Messages.Remove(m);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }


        //Methodes pour Messages Statut

        public void AfficherDetailsMessageStatut(long statusId, MessageStatus_Class statusClass)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == statusId);
                if (status != null)
                {
                    statusClass.MessagestatusID = status.MessagestatusID;
                    statusClass.MessageId = status.MessageId;
                    statusClass.UserId = status.UserId;
                    statusClass.IsRead = status.IsRead;
                    statusClass.ReadAt = status.ReadAt;
                }
            }
        }

        public int MarquerCommeLu(long messageId, long userId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessageId == messageId && x.UserId == userId);
                if (status != null)
                {
                    status.IsRead = 1;
                    status.ReadAt = DateTime.Now;
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        public int AjouterMessageEtStatusPourTous(long conversationId, long senderId, string contenu, string attachmentUrl = null)
        {
            using (var con = new Connection())
            {
                using (var transaction = con.Database.BeginTransaction())
                {
                    try
                    {
                        // 1. Création et ajout du message
                        var message = new Message
                        {
                            ConversationId = conversationId,
                            SenderId = senderId,
                            Contenu = contenu,
                            AttachmentUrl = attachmentUrl,
                            DateEnvoi = DateTime.Now,
                            name = "Nom ou pseudo de l'expéditeur" // adapte selon ton contexte
                        };

                        con.Messages.Add(message);
                        con.SaveChanges(); // Génère MessageId

                        // 2. Récupérer tous les participants de la conversation
                        var participants = con.ParticipantConversations
                                              .Where(pc => pc.ConversationId == conversationId)
                                              .Select(pc => pc.MembreId)
                                              .ToList();

                        // 3. Créer les MessageStatus pour tous
                        foreach (var membreId in participants)
                        {
                            var status = new MessageStatu
                            {
                                MessageId = message.MessageId,
                                UserId = (long)membreId,
                                IsRead = (membreId == senderId) ? 1 : 0,
                                ReadAt = (membreId == senderId) ? (DateTime?)DateTime.Now : null
                            };
                            con.MessageStatus.Add(status);
                        }

                        con.SaveChanges();
                        transaction.Commit();

                        return 1; // succès
                    }
                    catch
                    {
                        transaction.Rollback();
                        return 0; // échec
                    }
                }
            }
        }


        public int SupprimerMessageStatut(long messageStatusId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == messageStatusId);
                if (status != null)
                {
                    con.MessageStatus.Remove(status);
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        // Nouvelles méthodes implémentées
        public void RechercherMessages(Repeater rpt, long membreId, string motCle)
        {
            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               join pc in con.ParticipantConversations on m.ConversationId equals pc.ConversationId
                               where pc.MembreId == membreId &&
                                     (m.Contenu.Contains(motCle) || mb.Nom.Contains(motCle) || mb.Prenom.Contains(motCle))
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "",
                                   DateEnvoi = m.DateEnvoi,
                                   ConversationId = m.ConversationId
                               };

                rpt.DataSource = messages.Take(50).ToList();
                rpt.DataBind();
            }
        }

        public void ChargerConversationsRecentes(Repeater rpt, long membreId, int limite = 10)
        {
            using (Connection con = new Connection())
            {
                var conversations = from c in con.Conversations
                                   join pc in con.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                   where pc.MembreId == membreId
                                   let dernierMessage = con.Messages
                                       .Where(m => m.ConversationId == c.ConversationId)
                                       .OrderByDescending(m => m.DateEnvoi)
                                       .FirstOrDefault()
                                   let autreParticipant = con.ParticipantConversations
                                       .Where(p => p.ConversationId == c.ConversationId && p.MembreId != membreId)
                                       .Join(con.Membres, p => p.MembreId, m => m.MembreId, (p, m) => m)
                                       .FirstOrDefault()
                                   orderby dernierMessage.DateEnvoi descending
                                   select new
                                   {
                                       ConversationId = c.ConversationId,
                                       Sujet = c.Sujet,
                                       IsGroup = c.IsGroup,
                                       DernierMessage = dernierMessage.Contenu ?? "",
                                       DateDernierMessage = dernierMessage.DateEnvoi,
                                       NomAutreParticipant = c.IsGroup == 1 ? c.Sujet :
                                           (autreParticipant != null ? autreParticipant.Nom + " " + autreParticipant.Prenom : ""),
                                       PhotoAutreParticipant = autreParticipant != null ? autreParticipant.PhotoProfil : ""
                                   };

                rpt.DataSource = conversations.Take(limite).ToList();
                rpt.DataBind();
            }
        }

        public int CompterMessagesNonLusConversation(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.MessageStatus
                    .Count(ms => ms.UserId == membreId &&
                                ms.IsRead == 0 &&
                                con.Messages.Any(m => m.MessageId == ms.MessageId && m.ConversationId == conversationId));
            }
        }

        public void MarquerConversationCommeLue(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                var messagesNonLus = from ms in con.MessageStatus
                                    join m in con.Messages on ms.MessageId equals m.MessageId
                                    where m.ConversationId == conversationId &&
                                          ms.UserId == membreId &&
                                          ms.IsRead == 0
                                    select ms;

                foreach (var status in messagesNonLus)
                {
                    status.IsRead = 1;
                    status.ReadAt = DateTime.Now;
                }

                con.SaveChanges();
            }
        }

        public bool VerifierPermissionMessage(long messageId, long membreId)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'utilisateur est participant de la conversation du message
                return con.Messages
                    .Where(m => m.MessageId == messageId)
                    .Join(con.ParticipantConversations,
                          m => m.ConversationId,
                          pc => pc.ConversationId,
                          (m, pc) => pc)
                    .Any(pc => pc.MembreId == membreId);
            }
        }

        public void ChargerMessagesAvecPagination(Repeater rpt, long conversationId, int page, int taille = 20)
        {
            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = m.Contenu ?? "",
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? ""
                               };

                rpt.DataSource = messages.Skip((page - 1) * taille).Take(taille).ToList();
                rpt.DataBind();
            }
        }
    }
}