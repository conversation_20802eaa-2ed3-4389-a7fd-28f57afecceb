<!DOCTYPE html>
<html>
<head>
    <title>Test Anti-Doublon</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        input[type="text"] { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Anti-Doublon Messagerie</h1>
        
        <div class="test-section info">
            <h3>📋 Instructions de Test</h3>
            <ol>
                <li>Saisissez un message de test</li>
                <li>Cliquez sur "Envoyer" plusieurs fois rapidement</li>
                <li>Vérifiez qu'un seul message est traité</li>
                <li>Actualisez la page (F5) après envoi</li>
                <li>Vérifiez qu'aucun doublon n'est créé</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>💬 Simulateur de Messagerie</h3>
            <input type="text" id="testMessage" placeholder="Tapez votre message de test...">
            <button class="btn-primary" onclick="simulateMessageSend()">📤 Envoyer Message</button>
            <button class="btn-warning" onclick="simulateMultipleClicks()">⚡ Test Clics Multiples</button>
            <button class="btn-success" onclick="clearLog()">🗑️ Vider Log</button>
        </div>
        
        <div class="test-section">
            <h3>📊 Log des Actions</h3>
            <div id="actionLog" style="background: #f8f9fa; padding: 10px; border-radius: 5px; min-height: 200px; font-family: monospace; font-size: 12px;">
                <div style="color: #666;">Log des actions de test...</div>
            </div>
        </div>
        
        <div class="test-section success">
            <h3>✅ Fonctionnalités Testées</h3>
            <ul>
                <li id="test1">❌ Protection contre clics multiples</li>
                <li id="test2">❌ Validation du contenu du message</li>
                <li id="test3">❌ Désactivation du bouton pendant l'envoi</li>
                <li id="test4">❌ Réactivation après traitement</li>
                <li id="test5">❌ Gestion de l'historique du navigateur</li>
            </ul>
        </div>
    </div>

    <script>
        let isSubmitting = false;
        let lastMessageTime = 0;
        let messageCount = 0;
        
        function logAction(message, type = 'info') {
            const log = document.getElementById('actionLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#17a2b8';
            log.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        function simulateMessageSend() {
            const message = document.getElementById('testMessage').value.trim();
            
            // Test 1: Validation du contenu
            if (message === '') {
                logAction('❌ ERREUR: Message vide détecté', 'error');
                alert('Veuillez saisir un message.');
                updateTestStatus('test2', true);
                return false;
            }
            
            // Test 2: Protection contre clics multiples
            if (isSubmitting) {
                logAction('❌ BLOQUÉ: Envoi déjà en cours', 'warning');
                alert('Message en cours d\'envoi, veuillez patienter...');
                updateTestStatus('test1', true);
                return false;
            }
            
            // Test 3: Vérification temporelle
            const currentTime = Date.now();
            if (currentTime - lastMessageTime < 2000) {
                logAction('❌ BLOQUÉ: Envoi trop rapide (moins de 2 secondes)', 'warning');
                alert('Veuillez attendre avant d\'envoyer un autre message.');
                return false;
            }
            
            // Marquer comme en cours d'envoi
            isSubmitting = true;
            lastMessageTime = currentTime;
            messageCount++;
            
            // Test 4: Désactivation du bouton
            const button = event.target;
            button.disabled = true;
            button.innerHTML = '⏳ Envoi...';
            updateTestStatus('test3', true);
            
            logAction(`✅ MESSAGE #${messageCount}: "${message}" - Envoi en cours...`, 'success');
            
            // Simuler l'envoi (2 secondes)
            setTimeout(() => {
                isSubmitting = false;
                button.disabled = false;
                button.innerHTML = '📤 Envoyer Message';
                logAction(`✅ MESSAGE #${messageCount}: Envoyé avec succès!`, 'success');
                updateTestStatus('test4', true);
                
                // Vider le champ
                document.getElementById('testMessage').value = '';
            }, 2000);
            
            return true;
        }
        
        function simulateMultipleClicks() {
            logAction('🔥 TEST: Simulation de clics multiples rapides...', 'warning');
            document.getElementById('testMessage').value = 'Message de test clics multiples';
            
            // Simuler 5 clics rapides
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    logAction(`🖱️ Clic #${i + 1}`, 'info');
                    simulateMessageSend();
                }, i * 100);
            }
        }
        
        function clearLog() {
            document.getElementById('actionLog').innerHTML = '<div style="color: #666;">Log vidé - Prêt pour nouveaux tests...</div>';
            messageCount = 0;
            
            // Réinitialiser les tests
            for (let i = 1; i <= 5; i++) {
                updateTestStatus(`test${i}`, false);
            }
        }
        
        function updateTestStatus(testId, passed) {
            const element = document.getElementById(testId);
            if (passed) {
                element.innerHTML = element.innerHTML.replace('❌', '✅');
                element.style.color = '#28a745';
            } else {
                element.innerHTML = element.innerHTML.replace('✅', '❌');
                element.style.color = '#dc3545';
            }
        }
        
        // Test 5: Gestion de l'historique
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
            updateTestStatus('test5', true);
            logAction('✅ Historique du navigateur géré', 'success');
        }
        
        // Initialisation
        window.onload = function() {
            logAction('🚀 Simulateur de test anti-doublon initialisé', 'info');
            logAction('📝 Saisissez un message et testez les fonctionnalités', 'info');
        };
    </script>
</body>
</html>
