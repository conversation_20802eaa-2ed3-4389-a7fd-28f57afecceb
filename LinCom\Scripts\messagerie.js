// Fonctionnalités JavaScript pour la messagerie LinCom

// Variables globales
let currentConversationId = 0;
let currentUserId = 0;
let isTyping = false;
let typingTimer;

// Initialisation
$(document).ready(function() {
    initializeMessagerie();
    setupEventHandlers();
    startAutoRefresh();
});

function initializeMessagerie() {
    // Récupérer les IDs depuis les champs cachés
    currentUserId = parseInt($('#hdnCurrentUserId').val()) || 0;
    currentConversationId = parseInt($('#hdnConversationId').val()) || 0;
    
    // Faire défiler vers le bas des messages
    scrollToBottom();
    
    // Marquer la conversation comme lue
    if (currentConversationId > 0) {
        marquerConversationCommeLue(currentConversationId);
    }
}

function setupEventHandlers() {
    // Gestion de l'envoi de message avec Enter
    $('#txtMessage').on('keypress', function(e) {
        if (e.which === 13 && !e.shiftKey) {
            e.preventDefault();
            envoyerMessage();
        }
    });
    
    // Indicateur de frappe
    $('#txtMessage').on('input', function() {
        if (!isTyping) {
            isTyping = true;
            // Ici on pourrait envoyer un signal "en train d'écrire"
        }
        
        clearTimeout(typingTimer);
        typingTimer = setTimeout(function() {
            isTyping = false;
            // Arrêter l'indicateur "en train d'écrire"
        }, 1000);
    });
    
    // Recherche de messages
    $('#searchMessages').on('input', function() {
        const searchTerm = $(this).val();
        if (searchTerm.length > 2) {
            rechercherMessages(searchTerm);
        } else if (searchTerm.length === 0) {
            chargerMessages();
        }
    });
    
    // Gestion des fichiers
    $('#fileInput').on('change', function() {
        const files = this.files;
        if (files.length > 0) {
            previewFiles(files);
        }
    });
}

function envoyerMessage() {
    const message = $('#txtMessage').val().trim();
    if (message === '') {
        showAlert('Veuillez saisir un message.', 'warning');
        return;
    }
    
    if (currentConversationId === 0) {
        showAlert('Veuillez sélectionner un destinataire.', 'warning');
        return;
    }
    
    // Désactiver le bouton d'envoi temporairement
    $('#btnenvoie').prop('disabled', true);
    
    // Déclencher l'envoi côté serveur
    __doPostBack('btnenvoie', '');
}

function chargerMessages() {
    if (currentConversationId > 0) {
        // Recharger les messages via AJAX si nécessaire
        // Pour l'instant, on utilise le postback
        __doPostBack('refreshMessages', '');
    }
}

function rechercherMessages(searchTerm) {
    // Implémenter la recherche via AJAX
    $.ajax({
        type: 'POST',
        url: 'messagerie.aspx/RechercherMessages',
        data: JSON.stringify({ 
            membreId: currentUserId, 
            motCle: searchTerm 
        }),
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        success: function(response) {
            // Afficher les résultats de recherche
            afficherResultatsRecherche(response.d);
        },
        error: function() {
            showAlert('Erreur lors de la recherche.', 'error');
        }
    });
}

function marquerConversationCommeLue(conversationId) {
    $.ajax({
        type: 'POST',
        url: 'messagerie.aspx/MarquerCommeLue',
        data: JSON.stringify({ 
            conversationId: conversationId, 
            membreId: currentUserId 
        }),
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        success: function(response) {
            // Mettre à jour les indicateurs visuels
            updateUnreadIndicators();
        }
    });
}

function scrollToBottom() {
    const chatBody = $('.chat-body');
    if (chatBody.length > 0) {
        chatBody.scrollTop(chatBody[0].scrollHeight);
    }
}

function showAlert(message, type = 'info') {
    // Créer une notification toast
    const alertClass = type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 
                      type === 'success' ? 'alert-success' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    // Auto-dismiss après 5 secondes
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

function showSuccessMessage(message) {
    showAlert(message, 'success');
    scrollToBottom();
}

function previewFiles(files) {
    const previewContainer = $('#filePreview');
    previewContainer.empty();
    
    Array.from(files).forEach(file => {
        const fileDiv = $(`
            <div class="file-preview-item">
                <span class="file-name">${file.name}</span>
                <span class="file-size">(${formatFileSize(file.size)})</span>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFilePreview(this)">×</button>
            </div>
        `);
        previewContainer.append(fileDiv);
    });
    
    previewContainer.show();
}

function removeFilePreview(button) {
    $(button).parent().remove();
    if ($('#filePreview .file-preview-item').length === 0) {
        $('#filePreview').hide();
        $('#fileInput').val('');
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function updateUnreadIndicators() {
    // Mettre à jour les badges de messages non lus
    $('.contact-item').each(function() {
        const contactId = $(this).data('contact-id');
        // Récupérer le nombre de messages non lus pour ce contact
        // et mettre à jour l'indicateur visuel
    });
}

function startAutoRefresh() {
    // Actualiser les messages toutes les 30 secondes
    setInterval(function() {
        if (currentConversationId > 0) {
            chargerMessages();
        }
    }, 30000);
}

// Fonctions utilitaires
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'À l\'instant';
    if (diff < 3600000) return Math.floor(diff / 60000) + ' min';
    if (diff < 86400000) return Math.floor(diff / 3600000) + ' h';
    
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Gestion des émojis (optionnel)
function toggleEmojiPicker() {
    $('#emojiPicker').toggle();
}

function insertEmoji(emoji) {
    const textarea = document.getElementById('txtMessage');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;
    
    textarea.value = text.substring(0, start) + emoji + text.substring(end);
    textarea.selectionStart = textarea.selectionEnd = start + emoji.length;
    textarea.focus();
    
    $('#emojiPicker').hide();
}
