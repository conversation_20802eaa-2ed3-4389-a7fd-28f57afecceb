using System;
using System.IO;
using System.Text;
using System.Web;

namespace LinCom
{
    public partial class TestFichiers : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                AfficherInfoDossier();
                ListerFichiersExistants();
                TesterAccesURL();
            }
        }

        private void AfficherInfoDossier()
        {
            StringBuilder sb = new StringBuilder();
            
            try
            {
                string dossierPhysique = Server.MapPath("~/file/messages/");
                string dossierVirtuel = "~/file/messages/";
                
                sb.Append("<ul>");
                sb.Append($"<li><strong>Chemin virtuel :</strong> {dossierVirtuel}</li>");
                sb.Append($"<li><strong>Chemin physique :</strong> {dossierPhysique}</li>");
                sb.Append($"<li><strong>Dossier existe :</strong> {Directory.Exists(dossierPhysique)}</li>");
                
                if (Directory.Exists(dossierPhysique))
                {
                    DirectoryInfo di = new DirectoryInfo(dossierPhysique);
                    sb.Append($"<li><strong>Permissions lecture :</strong> {TestPermissionLecture(dossierPhysique)}</li>");
                    sb.Append($"<li><strong>Permissions écriture :</strong> {TestPermissionEcriture(dossierPhysique)}</li>");
                }
                else
                {
                    // Créer le dossier
                    Directory.CreateDirectory(dossierPhysique);
                    sb.Append($"<li><strong>Dossier créé :</strong> {Directory.Exists(dossierPhysique)}</li>");
                }
                
                sb.Append("</ul>");
                
                litInfoDossier.Text = sb.ToString();
            }
            catch (Exception ex)
            {
                litInfoDossier.Text = $"<div class='error'>Erreur : {ex.Message}</div>";
            }
        }

        private bool TestPermissionLecture(string dossier)
        {
            try
            {
                Directory.GetFiles(dossier);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private bool TestPermissionEcriture(string dossier)
        {
            try
            {
                string testFile = Path.Combine(dossier, "test_write.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private void ListerFichiersExistants()
        {
            StringBuilder sb = new StringBuilder();
            
            try
            {
                string dossier = Server.MapPath("~/file/messages/");
                
                if (Directory.Exists(dossier))
                {
                    string[] fichiers = Directory.GetFiles(dossier);
                    
                    if (fichiers.Length == 0)
                    {
                        sb.Append("<p>Aucun fichier trouvé dans le dossier.</p>");
                    }
                    else
                    {
                        sb.Append($"<p><strong>{fichiers.Length} fichier(s) trouvé(s) :</strong></p>");
                        
                        foreach (string fichier in fichiers)
                        {
                            FileInfo fi = new FileInfo(fichier);
                            string nomFichier = fi.Name;
                            string urlVirtuelle = ResolveUrl($"~/file/messages/{nomFichier}");
                            
                            sb.Append("<div class='file-item'>");
                            sb.Append($"<strong>{nomFichier}</strong><br>");
                            sb.Append($"Taille : {fi.Length} bytes<br>");
                            sb.Append($"Date : {fi.LastWriteTime}<br>");
                            sb.Append($"URL : <a href='{urlVirtuelle}' target='_blank'>{urlVirtuelle}</a>");
                            sb.Append("</div>");
                        }
                    }
                }
                else
                {
                    sb.Append("<p class='error'>Le dossier n'existe pas.</p>");
                }
                
                litFichiersExistants.Text = sb.ToString();
            }
            catch (Exception ex)
            {
                litFichiersExistants.Text = $"<div class='error'>Erreur : {ex.Message}</div>";
            }
        }

        private void TesterAccesURL()
        {
            StringBuilder sb = new StringBuilder();
            
            try
            {
                // Créer un fichier de test
                string dossier = Server.MapPath("~/file/messages/");
                string nomFichierTest = "test_access.txt";
                string cheminTest = Path.Combine(dossier, nomFichierTest);
                
                if (!Directory.Exists(dossier))
                    Directory.CreateDirectory(dossier);
                
                File.WriteAllText(cheminTest, $"Fichier de test créé le {DateTime.Now}");
                
                string urlTest = ResolveUrl($"~/file/messages/{nomFichierTest}");
                
                sb.Append("<div class='info'>");
                sb.Append($"<p><strong>Fichier de test créé :</strong> {nomFichierTest}</p>");
                sb.Append($"<p><strong>URL de test :</strong> <a href='{urlTest}' target='_blank'>{urlTest}</a></p>");
                sb.Append("<p><strong>Instructions :</strong> Cliquez sur le lien ci-dessus. Si vous voyez le contenu du fichier, l'accès fonctionne correctement.</p>");
                sb.Append("</div>");
                
                litTestURL.Text = sb.ToString();
            }
            catch (Exception ex)
            {
                litTestURL.Text = $"<div class='error'>Erreur : {ex.Message}</div>";
            }
        }

        protected void btnTestUpload_Click(object sender, EventArgs e)
        {
            try
            {
                if (testFileUpload.HasFile)
                {
                    string dossier = Server.MapPath("~/file/messages/");
                    string nomFichier = $"test_{DateTime.Now:yyyyMMddHHmmss}_{testFileUpload.FileName}";
                    string cheminComplet = Path.Combine(dossier, nomFichier);
                    
                    testFileUpload.SaveAs(cheminComplet);
                    
                    string urlFichier = ResolveUrl($"~/file/messages/{nomFichier}");
                    
                    Response.Write($"<script>alert('Fichier uploadé avec succès!\\nURL: {urlFichier}');</script>");
                    
                    // Recharger la liste des fichiers
                    ListerFichiersExistants();
                }
                else
                {
                    Response.Write("<script>alert('Veuillez sélectionner un fichier.');</script>");
                }
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur upload: {ex.Message}');</script>");
            }
        }
    }
}
