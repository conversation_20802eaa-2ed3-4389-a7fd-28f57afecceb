using LinCom.Imp;
using LinCom.Model;
using System;
using System.Web;
using System.Web.UI;

namespace LinCom
{
    public partial class TestMessagerie : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LogMessage("Page de test chargée - Prêt pour les diagnostics");
            }
        }

        protected void btnRunTests_Click(object sender, EventArgs e)
        {
            LogMessage("🚀 Début des tests automatiques...");
            
            bool allTestsPassed = true;
            
            // Test 1: Authentification
            LogMessage("Test 1: Vérification de l'authentification...");
            if (TestAuthentication())
            {
                UpdateStatus("authStatus", "ok", "Connecté");
                LogMessage("✅ Authentification: OK");
            }
            else
            {
                UpdateStatus("authStatus", "error", "Non connecté");
                LogMessage("❌ Authentification: ÉCHEC");
                allTestsPassed = false;
            }
            
            // Test 2: Base de données
            LogMessage("Test 2: Connexion à la base de données...");
            if (TestDatabase())
            {
                UpdateStatus("dbStatus", "ok", "Connectée");
                LogMessage("✅ Base de données: OK");
            }
            else
            {
                UpdateStatus("dbStatus", "error", "Erreur");
                LogMessage("❌ Base de données: ÉCHEC");
                allTestsPassed = false;
            }
            
            // Test 3: Chargement des contacts
            LogMessage("Test 3: Chargement des contacts...");
            int contactCount = TestContacts();
            if (contactCount >= 0)
            {
                UpdateStatus("contactsStatus", "ok", $"{contactCount} trouvés");
                LogMessage($"✅ Contacts: {contactCount} contacts trouvés");
            }
            else
            {
                UpdateStatus("contactsStatus", "error", "Erreur");
                LogMessage("❌ Contacts: ÉCHEC");
                allTestsPassed = false;
            }
            
            // Test 4: Système de messages
            LogMessage("Test 4: Système de messages...");
            if (TestMessages())
            {
                UpdateStatus("messagesStatus", "ok", "Fonctionnel");
                LogMessage("✅ Messages: OK");
            }
            else
            {
                UpdateStatus("messagesStatus", "error", "Erreur");
                LogMessage("❌ Messages: ÉCHEC");
                allTestsPassed = false;
            }
            
            // Résultat final
            if (allTestsPassed)
            {
                lblTestResults.Text = "🎉 <strong>Tous les tests sont réussis !</strong><br>" +
                                     "La messagerie est prête à être utilisée.<br>" +
                                     "<span style='color: green;'>Vous pouvez maintenant accéder à la messagerie en toute sécurité.</span>";
                lblTestResults.CssClass = "success";
                ClientScript.RegisterStartupScript(this.GetType(), "showSuccess", "showSuccess();", true);
            }
            else
            {
                lblTestResults.Text = "⚠️ <strong>Certains tests ont échoué</strong><br>" +
                                     "Veuillez corriger les problèmes identifiés avant d'utiliser la messagerie.<br>" +
                                     "<span style='color: red;'>Consultez le log détaillé pour plus d'informations.</span>";
                lblTestResults.CssClass = "error";
            }
            
            LogMessage("🏁 Tests terminés.");
        }

        private bool TestAuthentication()
        {
            try
            {
                HttpCookie idCookie = Request.Cookies["iduser"];
                HttpCookie roleCookie = Request.Cookies["role"];
                
                if (idCookie != null && roleCookie != null)
                {
                    long userId;
                    if (long.TryParse(idCookie.Value, out userId) && userId > 0)
                    {
                        LogMessage($"Utilisateur authentifié: ID={userId}, Rôle={roleCookie.Value}");
                        return true;
                    }
                }
                
                LogMessage("Aucune authentification valide trouvée");
                return false;
            }
            catch (Exception ex)
            {
                LogMessage($"Erreur d'authentification: {ex.Message}");
                return false;
            }
        }

        private bool TestDatabase()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    // Test simple de connexion
                    var testQuery = con.Membres.Take(1).ToList();
                    LogMessage("Connexion à la base de données réussie");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Erreur de base de données: {ex.Message}");
                return false;
            }
        }

        private int TestContacts()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var contacts = con.Membres.Where(m => m.statut == "actif").Take(10).ToList();
                    LogMessage($"Requête contacts exécutée: {contacts.Count} résultats");
                    return contacts.Count;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Erreur lors du test des contacts: {ex.Message}");
                return -1;
            }
        }

        private bool TestMessages()
        {
            try
            {
                using (Connection con = new Connection())
                {
                    // Test de la table Messages
                    var messageCount = con.Messages.Take(1).Count();
                    LogMessage("Accès à la table Messages réussi");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Erreur lors du test des messages: {ex.Message}");
                return false;
            }
        }

        protected void btnCreateTestUser_Click(object sender, EventArgs e)
        {
            try
            {
                // Créer des cookies de test
                HttpCookie idCookie = new HttpCookie("iduser", "1");
                HttpCookie roleCookie = new HttpCookie("role", "admin");
                HttpCookie userCookie = new HttpCookie("usernm", "testuser");
                
                DateTime expiration = DateTime.Now.AddHours(2);
                idCookie.Expires = expiration;
                roleCookie.Expires = expiration;
                userCookie.Expires = expiration;
                
                Response.Cookies.Add(idCookie);
                Response.Cookies.Add(roleCookie);
                Response.Cookies.Add(userCookie);
                
                LogMessage("✅ Utilisateur de test créé avec succès");
                LogMessage($"ID: 1, Rôle: admin, Expiration: {expiration:dd/MM/yyyy HH:mm}");
                
                lblTestResults.Text = "👤 <strong>Utilisateur de test créé !</strong><br>" +
                                     "Vous pouvez maintenant relancer les tests.";
                lblTestResults.CssClass = "success";
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Erreur lors de la création de l'utilisateur test: {ex.Message}");
                lblTestResults.Text = "❌ <strong>Erreur</strong><br>" + ex.Message;
                lblTestResults.CssClass = "error";
            }
        }

        protected void btnTestDatabase_Click(object sender, EventArgs e)
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var memberCount = con.Membres.Count();
                    var messageCount = con.Messages.Count();
                    var conversationCount = con.Conversations.Count();
                    
                    LogMessage($"📊 Statistiques de la base de données:");
                    LogMessage($"- Membres: {memberCount}");
                    LogMessage($"- Messages: {messageCount}");
                    LogMessage($"- Conversations: {conversationCount}");
                    
                    lblTestResults.Text = $"🗄️ <strong>Base de données accessible !</strong><br>" +
                                         $"Membres: {memberCount}, Messages: {messageCount}, Conversations: {conversationCount}";
                    lblTestResults.CssClass = "success";
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Erreur de base de données: {ex.Message}");
                lblTestResults.Text = "❌ <strong>Erreur de base de données</strong><br>" + ex.Message;
                lblTestResults.CssClass = "error";
            }
        }

        protected void btnClearCache_Click(object sender, EventArgs e)
        {
            try
            {
                // Vider le cache de l'application
                foreach (string key in HttpContext.Current.Cache.Cast<System.Collections.DictionaryEntry>().Select(entry => entry.Key.ToString()).ToList())
                {
                    HttpContext.Current.Cache.Remove(key);
                }
                
                LogMessage("🗑️ Cache de l'application vidé");
                lblTestResults.Text = "🗑️ <strong>Cache vidé avec succès !</strong>";
                lblTestResults.CssClass = "success";
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Erreur lors du vidage du cache: {ex.Message}");
                lblTestResults.Text = "❌ <strong>Erreur</strong><br>" + ex.Message;
                lblTestResults.CssClass = "error";
            }
        }

        protected void btnGoToMessagerie_Click(object sender, EventArgs e)
        {
            Response.Redirect("messagerie.aspx");
        }

        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            string currentLog = lblDetailedLog.Text;
            
            if (currentLog == "Prêt pour les tests...")
            {
                currentLog = "";
            }
            
            lblDetailedLog.Text = currentLog + $"[{timestamp}] {message}<br>";
        }

        private void UpdateStatus(string elementId, string status, string message)
        {
            string script = $"updateStatus('{elementId}', '{status}', '{message}');";
            ClientScript.RegisterStartupScript(this.GetType(), $"update_{elementId}", script, true);
        }
    }
}
