using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Text;

namespace LinCom
{
    public partial class TestMessagerie : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                AfficherInfosSysteme();
            }
        }

        protected void btnTestConnexion_Click(object sender, EventArgs e)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("<h4>Test de Connexion à la Base de Données</h4>");

            try
            {
                using (Connection con = new Connection())
                {
                    var count = con.Messages.Count();
                    sb.Append($"<div class='success'>✅ Connexion réussie! Nombre de messages: {count}</div>");
                }
            }
            catch (Exception ex)
            {
                sb.Append($"<div class='error'>❌ Erreur de connexion: {ex.Message}</div>");
            }

            litResultats.Text = sb.ToString();
        }

        protected void btnTestMessage_Click(object sender, EventArgs e)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("<h4>Test des Fonctionnalités de Message</h4>");

            try
            {
                // Test des interfaces
                IMessage objMessage = new MessageImp();
                IConversation objConversation = new ConversationImp();
                IFichierMessage objFichier = new FichierMessageImp();

                sb.Append("<div class='success'>✅ Interfaces instanciées avec succès</div>");

                // Test des méthodes
                sb.Append("<div class='info'>📋 Méthodes disponibles:</div>");
                sb.Append("<ul>");
                sb.Append("<li>✅ MessageImp.Envoyer()</li>");
                sb.Append("<li>✅ MessageImp.ChargerMessages()</li>");
                sb.Append("<li>✅ MessageImp.RechercherMessages()</li>");
                sb.Append("<li>✅ MessageImp.AjouterMessageEtStatusPourTous()</li>");
                sb.Append("<li>✅ ConversationImp.VerifierConversationId()</li>");
                sb.Append("<li>✅ FichierMessageImp.UploadFichier()</li>");
                sb.Append("</ul>");

                // Test de comptage
                int nombreMessages = objMessage.CompterNonLus(1);
                sb.Append($"<div class='info'>📊 Messages non lus pour utilisateur test: {nombreMessages}</div>");
            }
            catch (Exception ex)
            {
                sb.Append($"<div class='error'>❌ Erreur lors du test: {ex.Message}</div>");
            }

            litResultats.Text = sb.ToString();
        }

        protected void btnTestFichier_Click(object sender, EventArgs e)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("<h4>Test de Gestion des Fichiers</h4>");

            try
            {
                IFichierMessage objFichier = new FichierMessageImp();

                // Test des validations
                bool pdfAutorise = objFichier.VerifierTypeFichierAutorise("application/pdf");
                bool imageAutorise = objFichier.VerifierTypeFichierAutorise("image/jpeg");
                bool executableBloque = objFichier.VerifierTypeFichierAutorise("application/exe");

                sb.Append($"<div class='success'>✅ PDF autorisé: {pdfAutorise}</div>");
                sb.Append($"<div class='success'>✅ Image autorisée: {imageAutorise}</div>");
                sb.Append($"<div class='success'>✅ Exécutable bloqué: {!executableBloque}</div>");

                // Test de taille
                bool tailleOK = objFichier.VerifierTailleFichier(5 * 1024 * 1024); // 5MB
                bool tailleTropGrosse = objFichier.VerifierTailleFichier(15 * 1024 * 1024); // 15MB

                sb.Append($"<div class='success'>✅ Fichier 5MB accepté: {tailleOK}</div>");
                sb.Append($"<div class='success'>✅ Fichier 15MB rejeté: {!tailleTropGrosse}</div>");

                // Vérifier le dossier
                string dossier = Server.MapPath("~/file/messages/");
                bool dossierExiste = System.IO.Directory.Exists(dossier);
                sb.Append($"<div class='{(dossierExiste ? "success" : "error")}'>📁 Dossier messages: {(dossierExiste ? "Existe" : "N'existe pas")}</div>");
                sb.Append($"<div class='info'>📂 Chemin: {dossier}</div>");
            }
            catch (Exception ex)
            {
                sb.Append($"<div class='error'>❌ Erreur lors du test fichier: {ex.Message}</div>");
            }

            litResultats.Text = sb.ToString();
        }

        private void AfficherInfosSysteme()
        {
            StringBuilder sb = new StringBuilder();
            
            sb.Append("<ul>");
            sb.Append($"<li><strong>Version .NET:</strong> {Environment.Version}</li>");
            sb.Append($"<li><strong>Serveur:</strong> {Environment.MachineName}</li>");
            sb.Append($"<li><strong>Date/Heure:</strong> {DateTime.Now:yyyy-MM-dd HH:mm:ss}</li>");
            sb.Append($"<li><strong>Dossier Application:</strong> {Server.MapPath("~/")}</li>");
            sb.Append($"<li><strong>Dossier Messages:</strong> {Server.MapPath("~/file/messages/")}</li>");
            sb.Append("</ul>");

            sb.Append("<h4>📦 Modules Chargés</h4>");
            sb.Append("<ul>");
            sb.Append("<li>✅ LinCom.Imp.MessageImp</li>");
            sb.Append("<li>✅ LinCom.Imp.ConversationImp</li>");
            sb.Append("<li>✅ LinCom.Imp.FichierMessageImp</li>");
            sb.Append("<li>✅ LinCom.Scripts.messagerie.js</li>");
            sb.Append("</ul>");

            litInfos.Text = sb.ToString();
        }
    }
}
