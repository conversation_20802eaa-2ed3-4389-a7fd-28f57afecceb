using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class FichierMessageImp : IFichierMessage
    {
        private FichierMessage fichierMessage = new FichierMessage();
        
        // Types de fichiers autorisés
        private readonly string[] typesAutorises = {
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp",
            "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain", "application/zip", "application/rar"
        };
        
        // Taille maximale : 10 MB
        private readonly long tailleMaximale = 10 * 1024 * 1024;

        public int AjouterFichier(FichierMessage_Class fichier)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    fichierMessage.MessageId = (int?)fichier.MessageId;
                    fichierMessage.NomFichier = fichier.NomFichier;
                    fichierMessage.UrlFichier = fichier.UrlFichier;
                    fichierMessage.Name = fichier.Name;

                    con.FichierMessages.Add(fichierMessage);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public void AfficherDetails(long fichierId, FichierMessage_Class fichier)
        {
            using (Connection con = new Connection())
            {
                var f = con.FichierMessages.FirstOrDefault(x => x.FichierId == fichierId);
                if (f != null)
                {
                    fichier.FichierId = f.FichierId;
                    fichier.MessageId = f.MessageId;
                    fichier.NomFichier = f.NomFichier;
                    fichier.UrlFichier = f.UrlFichier;
                    fichier.Name = f.Name;
                }
            }
        }

        public void ChargerFichiersConversation(Repeater rpt, long conversationId)
        {
            using (Connection con = new Connection())
            {
                var fichiers = from f in con.FichierMessages
                              join m in con.Messages on f.MessageId equals m.MessageId
                              where m.ConversationId == conversationId
                              orderby f.FichierId descending
                              select new
                              {
                                  FichierId = f.FichierId,
                                  NomFichier = f.NomFichier,
                                  UrlFichier = f.UrlFichier,
                                  MessageId = f.MessageId,
                                  DateMessage = m.DateEnvoi
                              };

                rpt.DataSource = fichiers.ToList();
                rpt.DataBind();
            }
        }

        public void ChargerFichiersMessage(Repeater rpt, long messageId)
        {
            using (Connection con = new Connection())
            {
                var fichiers = from f in con.FichierMessages
                              where f.MessageId == messageId
                              select new
                              {
                                  FichierId = f.FichierId,
                                  NomFichier = f.NomFichier,
                                  UrlFichier = f.UrlFichier,
                                  Name = f.Name
                              };

                rpt.DataSource = fichiers.ToList();
                rpt.DataBind();
            }
        }

        public int CompterFichiersConversation(long conversationId)
        {
            using (Connection con = new Connection())
            {
                return con.FichierMessages
                    .Count(f => con.Messages.Any(m => m.MessageId == f.MessageId && m.ConversationId == conversationId));
            }
        }

        public List<FichierMessage_Class> ObtenirFichiersMessage(long messageId)
        {
            using (Connection con = new Connection())
            {
                var fichiers = con.FichierMessages
                    .Where(f => f.MessageId == messageId)
                    .Select(f => new FichierMessage_Class
                    {
                        FichierId = f.FichierId,
                        MessageId = f.MessageId,
                        NomFichier = f.NomFichier,
                        UrlFichier = f.UrlFichier,
                        Name = f.Name
                    })
                    .ToList();

                return fichiers;
            }
        }

        public int SupprimerFichier(long fichierId)
        {
            using (Connection con = new Connection())
            {
                var f = con.FichierMessages.FirstOrDefault(x => x.FichierId == fichierId);
                if (f != null)
                {
                    try
                    {
                        // Supprimer le fichier physique
                        if (File.Exists(HttpContext.Current.Server.MapPath(f.UrlFichier)))
                        {
                            File.Delete(HttpContext.Current.Server.MapPath(f.UrlFichier));
                        }

                        con.FichierMessages.Remove(f);
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public string UploadFichier(HttpPostedFile fichier, long membreId)
        {
            try
            {
                if (fichier == null || fichier.ContentLength == 0)
                    return null;

                if (!VerifierTypeFichierAutorise(fichier.ContentType))
                    throw new Exception("Type de fichier non autorisé");

                if (!VerifierTailleFichier(fichier.ContentLength))
                    throw new Exception("Fichier trop volumineux (max 10 MB)");

                // Créer le dossier s'il n'existe pas
                string dossier = HttpContext.Current.Server.MapPath("~/file/messages/");
                if (!Directory.Exists(dossier))
                    Directory.CreateDirectory(dossier);

                // Générer un nom unique
                string extension = Path.GetExtension(fichier.FileName);
                string nomUnique = $"{membreId}_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid().ToString("N")[..8]}{extension}";
                string cheminComplet = Path.Combine(dossier, nomUnique);

                // Sauvegarder le fichier
                fichier.SaveAs(cheminComplet);

                return $"~/file/messages/{nomUnique}";
            }
            catch
            {
                return null;
            }
        }

        public bool VerifierPermissionFichier(long fichierId, long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.FichierMessages
                    .Where(f => f.FichierId == fichierId)
                    .Join(con.Messages, f => f.MessageId, m => m.MessageId, (f, m) => m)
                    .Join(con.ParticipantConversations, m => m.ConversationId, pc => pc.ConversationId, (m, pc) => pc)
                    .Any(pc => pc.MembreId == membreId);
            }
        }

        public bool VerifierTailleFichier(long taille)
        {
            return taille <= tailleMaximale;
        }

        public bool VerifierTypeFichierAutorise(string typeFichier)
        {
            return typesAutorises.Contains(typeFichier.ToLower());
        }
    }
}
