using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IFichierMessage
    {
        int AjouterFichier(FichierMessage_Class fichier);
        void AfficherDetails(long fichierId, FichierMessage_Class fichier);
        int SupprimerFichier(long fichierId);
        void ChargerFichiersMessage(Repeater rpt, long messageId);
        void ChargerFichiersConversation(Repeater rpt, long conversationId);
        bool VerifierPermissionFichier(long fichierId, long membreId);
        string UploadFichier(HttpPostedFile fichier, long membreId);
        bool VerifierTypeFichierAutorise(string typeFichier);
        bool VerifierTailleFichier(long taille);
        List<FichierMessage_Class> ObtenirFichiersMessage(long messageId);
        int CompterFichiersConversation(long conversationId);
    }
}
