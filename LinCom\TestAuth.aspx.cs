using System;
using System.Web;
using System.Web.UI;

namespace LinCom
{
    public partial class TestAuth : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                AnalyserAuthentification();
                AfficherCookies();
            }
        }

        private void AnalyserAuthentification()
        {
            try
            {
                HttpCookie idCookie = Request.Cookies["iduser"];
                HttpCookie roleCookie = Request.Cookies["role"];
                
                if (idCookie != null && roleCookie != null)
                {
                    long userId;
                    if (long.TryParse(idCookie.Value, out userId) && userId > 0)
                    {
                        lblAuthStatus.Text = $"✅ <strong>Authentifié</strong><br>" +
                                           $"ID Utilisateur: {userId}<br>" +
                                           $"Rôle: {roleCookie.Value}<br>" +
                                           $"<span style='color: green;'>Prêt pour la messagerie!</span>";
                        lblAuthStatus.CssClass = "success";
                    }
                    else
                    {
                        lblAuthStatus.Text = "❌ <strong>Erreur</strong><br>" +
                                           "ID utilisateur invalide dans le cookie<br>" +
                                           "<span style='color: red;'>Authentification requise</span>";
                        lblAuthStatus.CssClass = "error";
                    }
                }
                else
                {
                    lblAuthStatus.Text = "⚠️ <strong>Non Authentifié</strong><br>" +
                                       "Cookies d'authentification manquants<br>" +
                                       "<span style='color: orange;'>Connexion requise</span>";
                    lblAuthStatus.CssClass = "warning";
                }
            }
            catch (Exception ex)
            {
                lblAuthStatus.Text = $"❌ <strong>Erreur</strong><br>Exception: {ex.Message}";
                lblAuthStatus.CssClass = "error";
            }
        }

        private void AfficherCookies()
        {
            try
            {
                string cookieInfo = "<strong>Cookies trouvés:</strong><br>";
                
                if (Request.Cookies.Count > 0)
                {
                    foreach (string cookieName in Request.Cookies.AllKeys)
                    {
                        HttpCookie cookie = Request.Cookies[cookieName];
                        if (cookieName == "iduser" || cookieName == "role" || cookieName == "usernm")
                        {
                            cookieInfo += $"🍪 <strong>{cookieName}</strong>: {cookie.Value}<br>";
                        }
                    }
                }
                else
                {
                    cookieInfo += "❌ Aucun cookie trouvé";
                }
                
                lblCookies.Text = cookieInfo;
            }
            catch (Exception ex)
            {
                lblCookies.Text = $"❌ Erreur lors de l'analyse des cookies: {ex.Message}";
            }
        }

        protected void btnCreateTestSession_Click(object sender, EventArgs e)
        {
            try
            {
                // Créer des cookies de test
                HttpCookie idCookie = new HttpCookie("iduser", "1");
                HttpCookie roleCookie = new HttpCookie("role", "admin");
                HttpCookie userCookie = new HttpCookie("usernm", "testuser");
                
                // Définir l'expiration (1 heure)
                DateTime expiration = DateTime.Now.AddHours(1);
                idCookie.Expires = expiration;
                roleCookie.Expires = expiration;
                userCookie.Expires = expiration;
                
                // Ajouter les cookies à la réponse
                Response.Cookies.Add(idCookie);
                Response.Cookies.Add(roleCookie);
                Response.Cookies.Add(userCookie);
                
                lblLog.Text = "✅ <strong>Session de test créée!</strong><br>" +
                             "ID: 1, Rôle: admin, Nom: testuser<br>" +
                             "Expiration: " + expiration.ToString("dd/MM/yyyy HH:mm") + "<br>" +
                             "<span style='color: green;'>Vous pouvez maintenant accéder à la messagerie</span>";
                
                // Actualiser l'affichage
                AnalyserAuthentification();
                AfficherCookies();
            }
            catch (Exception ex)
            {
                lblLog.Text = $"❌ <strong>Erreur</strong><br>Impossible de créer la session: {ex.Message}";
            }
        }

        protected void btnGoToMessagerie_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier l'authentification avant de rediriger
                HttpCookie idCookie = Request.Cookies["iduser"];
                if (idCookie != null)
                {
                    long userId;
                    if (long.TryParse(idCookie.Value, out userId) && userId > 0)
                    {
                        lblLog.Text = "🚀 <strong>Redirection vers la messagerie...</strong>";
                        Response.Redirect("messagerie.aspx");
                    }
                    else
                    {
                        lblLog.Text = "❌ <strong>Erreur</strong><br>ID utilisateur invalide. Créez d'abord une session de test.";
                    }
                }
                else
                {
                    lblLog.Text = "⚠️ <strong>Attention</strong><br>Aucune authentification trouvée. Créez d'abord une session de test.";
                }
            }
            catch (Exception ex)
            {
                lblLog.Text = $"❌ <strong>Erreur</strong><br>Impossible d'accéder à la messagerie: {ex.Message}";
            }
        }

        protected void btnClearCookies_Click(object sender, EventArgs e)
        {
            try
            {
                // Supprimer les cookies d'authentification
                string[] cookiesToClear = { "iduser", "role", "usernm" };
                
                foreach (string cookieName in cookiesToClear)
                {
                    if (Request.Cookies[cookieName] != null)
                    {
                        HttpCookie cookie = new HttpCookie(cookieName);
                        cookie.Expires = DateTime.Now.AddDays(-1); // Expirer le cookie
                        Response.Cookies.Add(cookie);
                    }
                }
                
                lblLog.Text = "🗑️ <strong>Cookies supprimés!</strong><br>" +
                             "Tous les cookies d'authentification ont été effacés.<br>" +
                             "<span style='color: orange;'>Actualisez la page pour voir les changements</span>";
                
                // Actualiser l'affichage après un court délai
                Response.AddHeader("Refresh", "2");
            }
            catch (Exception ex)
            {
                lblLog.Text = $"❌ <strong>Erreur</strong><br>Impossible de supprimer les cookies: {ex.Message}";
            }
        }
    }
}
