<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        .messagerie-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .chat-area { display: flex; gap: 20px; height: 600px; }
        .contacts-list { width: 300px; border: 1px solid #ddd; border-radius: 8px; overflow-y: auto; }
        .chat-window { flex: 1; border: 1px solid #ddd; border-radius: 8px; display: flex; flex-direction: column; }
        .chat-header { padding: 15px; background: #f8f9fa; border-bottom: 1px solid #ddd; }
        .chat-body { flex: 1; padding: 15px; overflow-y: auto; background: #fff; }
        .chat-footer { padding: 15px; border-top: 1px solid #ddd; background: #f8f9fa; }
        .message-input-area { display: flex; gap: 10px; align-items: flex-end; }
        .message-input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 20px; resize: none; }
        .btn-send { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 20px; cursor: pointer; }
        .btn-file { padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 15px; cursor: pointer; }
        .file-preview { margin: 10px 0; padding: 10px; background: #e9ecef; border-radius: 8px; display: none; }
        .search-area { margin-bottom: 15px; position: relative; }
        .search-input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 20px; }
        .search-results { background: white; border: 1px solid #ddd; border-radius: 8px; max-height: 200px; overflow-y: auto; display: none; position: absolute; z-index: 1000; width: 100%; }
        .search-item { padding: 10px; border-bottom: 1px solid #eee; cursor: pointer; }
        .search-item:hover { background: #f8f9fa; }
        .contact-item { padding: 10px; border-bottom: 1px solid #eee; cursor: pointer; text-decoration: none; color: #333; display: block; }
        .contact-item:hover { background: #f8f9fa; text-decoration: none; color: #333; }
    </style>
    
    <script>
        function selectFile() {
            document.getElementById('fileInput').click();
        }
        
        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                const preview = document.getElementById('filePreview');
                const fileName = file.name;
                const fileSize = (file.size / 1024).toFixed(1) + ' KB';
                
                preview.innerHTML = 
                    '<div style="display: flex; align-items: center; justify-content: space-between;">' +
                    '<span>📎 ' + fileName + ' (' + fileSize + ')</span>' +
                    '<button type="button" onclick="removeFile()" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 25px; height: 25px; cursor: pointer;">×</button>' +
                    '</div>';
                preview.style.display = 'block';
                document.getElementById('<%= btnSendFile.ClientID %>').style.display = 'inline-block';
            }
        }
        
        function removeFile() {
            document.getElementById('fileInput').value = '';
            document.getElementById('filePreview').style.display = 'none';
            document.getElementById('<%= btnSendFile.ClientID %>').style.display = 'none';
        }
        
        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            const resultsDiv = document.getElementById('searchResults');
            
            if (searchTerm.length < 2) {
                resultsDiv.style.display = 'none';
                return;
            }
            
            // Appel AJAX pour rechercher
            fetch('messagerie.aspx/RechercherMessages', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ motCle: searchTerm })
            })
            .then(response => response.json())
            .then(data => {
                showSearchResults(data.d || []);
            })
            .catch(error => {
                console.error('Erreur de recherche:', error);
                resultsDiv.style.display = 'none';
            });
        }
        
        function showSearchResults(results) {
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '';
            
            if (results.length > 0) {
                results.forEach(result => {
                    const item = document.createElement('div');
                    item.className = 'search-item';
                    item.innerHTML = '<strong>' + result.expediteur + '</strong>: ' + result.contenu;
                    item.onclick = function() { selectSearchResult(result); };
                    resultsDiv.appendChild(item);
                });
                resultsDiv.style.display = 'block';
            } else {
                resultsDiv.innerHTML = '<div class="search-item">Aucun résultat trouvé</div>';
                resultsDiv.style.display = 'block';
            }
        }
        
        function selectSearchResult(result) {
            document.getElementById('searchInput').value = '';
            document.getElementById('searchResults').style.display = 'none';
        }
        
        // Empêcher la duplication
        var isSubmitting = false;
        function preventDuplicate() {
            if (isSubmitting) {
                alert('Envoi en cours...');
                return false;
            }
            isSubmitting = true;
            setTimeout(function() { isSubmitting = false; }, 2000);
            return true;
        }

        // Fonction de validation des fichiers (fallback si le script externe ne charge pas)
        function validateFileUpload() {
            const fileInput = document.getElementById('fileInput');
            if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                alert('❌ Aucun fichier sélectionné');
                return false;
            }

            const file = fileInput.files[0];
            const maxSize = 10 * 1024 * 1024; // 10 MB
            if (file.size > maxSize) {
                alert('❌ Le fichier est trop volumineux (max 10 MB)');
                return false;
            }

            return preventDuplicate();
        }
    </script>

    <!-- Script de messagerie amélioré -->
    <script src="Scripts/messagerie-simple.js"></script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="messagerie-container">
        <h2>💬 Messagerie</h2>
        
        <!-- Zone de recherche -->
        <div class="search-area">
            <input type="text" id="searchInput" class="search-input" placeholder="🔍 Rechercher dans les messages..." 
                   onkeyup="performSearch()" />
            <div id="searchResults" class="search-results"></div>
        </div>
        
        <div class="chat-area">
            <!-- Liste des contacts -->
            <div class="contacts-list">
                <h4 style="padding: 15px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #ddd;">Contacts</h4>
                <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                    <ItemTemplate>
                        <asp:LinkButton ID="lnkContact" runat="server"
                                        CommandName="SelectContact"
                                        CommandArgument='<%# Eval("id") %>'
                                        CssClass="contact-item">
                            <strong><%# Eval("Nom") %> <%# Eval("Prenom") %></strong><br>
                            <small style="color: #666;"><%# Eval("Email") %></small>
                        </asp:LinkButton>
                    </ItemTemplate>
                </asp:ListView>
            </div>
            
            <!-- Zone de chat -->
            <div class="chat-window">
                <div class="chat-header">
                    <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact"></asp:Label>
                    <asp:Label ID="lblId" runat="server" Text="0" Visible="false"></asp:Label>
                </div>
                
                <div class="chat-body">
                    <asp:Repeater ID="rptMessages" runat="server">
                        <ItemTemplate>
                            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                                <strong><%# Eval("Expediteur") %></strong>
                                <small style="color: #666; float: right;"><%# Eval("DateEnvoi", "{0:dd/MM/yyyy HH:mm}") %></small>
                                <br>
                                <%# Eval("Contenu") %>
                                <%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) ? 
                                    "<br><a href='" + Eval("AttachmentUrl") + "' target='_blank'>📎 " + Eval("name") + "</a>" : "" %>
                            </div>
                        </ItemTemplate>
                    </asp:Repeater>
                </div>
                
                <div class="chat-footer">
                    <!-- Prévisualisation du fichier -->
                    <div id="filePreview" class="file-preview"></div>
                    
                    <!-- Zone de saisie -->
                    <div class="message-input-area">
                        <button type="button" class="btn-file" onclick="selectFile()">📎</button>
                        <textarea runat="server" id="txtMessage" class="message-input" rows="2" 
                                  placeholder="Tapez votre message..."></textarea>
                        <button type="button" runat="server" id="btnenvoie" class="btn-send" 
                                onserverclick="btnenvoie_ServerClick" onclick="return preventDuplicate()">
                            Envoyer
                        </button>
                        <button type="button" runat="server" id="btnSendFile" class="btn-send"
                                onserverclick="btnSendFile_ServerClick" onclick="return validateFileUpload()"
                                style="display: none;">
                            📎 Envoyer avec fichier
                        </button>
                    </div>
                    
                    <!-- Input fichier caché -->
                    <input type="file" id="fileInput" style="display: none;"
                           onchange="handleFileSelect(this)"
                           accept="image/*,.pdf,.doc,.docx,.txt" />
                </div>
            </div>
        </div>

        <!-- Champs cachés pour la gestion de l'état -->
        <asp:HiddenField ID="hdnCurrentUserId" runat="server" />
        <asp:HiddenField ID="hdnConversationId" runat="server" />
        <asp:HiddenField ID="hdnIsGroup" runat="server" />
        <asp:HiddenField ID="hdnLastMessageHash" runat="server" />
    </div>
</asp:Content>
