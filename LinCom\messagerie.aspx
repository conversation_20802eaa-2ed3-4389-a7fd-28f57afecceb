<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Script de messagerie -->
    <script src="Scripts/messagerie.js"></script>

    <!-- Script pour empêcher la duplication -->
    <script type="text/javascript">
        var formSubmitted = false;

        function preventDuplicateSubmission() {
            if (formSubmitted) {
                alert('Message en cours d\'envoi, veuillez patienter...');
                return false;
            }

            var message = document.getElementById('<%= txtMessage.ClientID %>').value.trim();
            if (message === '') {
                alert('Veuillez saisir un message.');
                return false;
            }

            formSubmitted = true;
            document.getElementById('<%= btnenvoie.ClientID %>').disabled = true;
            document.getElementById('<%= btnenvoie.ClientID %>').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Envoi...';

            return true;
        }

        // Réinitialiser après le chargement de la page
        window.onload = function() {
            formSubmitted = false;
            document.getElementById('<%= btnenvoie.ClientID %>').disabled = false;
            document.getElementById('<%= btnenvoie.ClientID %>').innerHTML = '<i class="fas fa-paper-plane"></i>';
        };

        // Empêcher la re-soumission lors de l'actualisation
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }

        // Fonction pour gérer la sélection de fichiers
        function handleFileSelection(fileInput) {
            const files = fileInput.files;
            if (files.length > 0) {
                showFilePreview(files);
                document.getElementById('<%= btnEnvoyerAvecFichier.ClientID %>').style.display = 'inline-block';
                document.getElementById('<%= btnenvoie.ClientID %>').style.display = 'none';
            }
        }

        function showFilePreview(files) {
            const previewContainer = document.getElementById('filePreview');
            if (!previewContainer) {
                // Créer le conteneur de prévisualisation s'il n'existe pas
                const container = document.createElement('div');
                container.id = 'filePreview';
                container.className = 'file-preview-container';
                container.style.display = 'block';

                const messageContainer = document.querySelector('.message-input-container');
                messageContainer.parentNode.insertBefore(container, messageContainer);
            }

            const preview = document.getElementById('filePreview');
            preview.innerHTML = '';

            Array.from(files).forEach((file, index) => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-preview-item';
                fileDiv.innerHTML = `
                    <div class="file-info">
                        <i class="fas fa-file"></i>
                        <span class="file-name">${file.name}</span>
                        <span class="file-size">(${formatFileSize(file.size)})</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                preview.appendChild(fileDiv);
            });

            preview.style.display = 'block';
        }

        function removeFile(index) {
            const fileInput = document.getElementById('<%= fileUpload.ClientID %>');
            fileInput.value = '';
            document.getElementById('filePreview').style.display = 'none';
            document.getElementById('<%= btnEnvoyerAvecFichier.ClientID %>').style.display = 'none';
            document.getElementById('<%= btnenvoie.ClientID %>').style.display = 'inline-block';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function resetFileUpload() {
            const fileInput = document.getElementById('<%= fileUpload.ClientID %>');
            const preview = document.getElementById('filePreview');

            fileInput.value = '';
            if (preview) preview.style.display = 'none';

            document.getElementById('<%= btnEnvoyerAvecFichier.ClientID %>').style.display = 'none';
            document.getElementById('<%= btnenvoie.ClientID %>').style.display = 'inline-block';
        }

        // Fonctions de recherche
        function toggleSearchBox() {
            const searchContainer = document.getElementById('searchContainer');
            const chatBody = document.getElementById('chatBody');

            if (searchContainer.style.display === 'none') {
                searchContainer.style.display = 'block';
                chatBody.style.display = 'none';
                document.getElementById('searchInput').focus();
            } else {
                clearSearch();
            }
        }

        function clearSearch() {
            const searchContainer = document.getElementById('searchContainer');
            const chatBody = document.getElementById('chatBody');
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');

            searchContainer.style.display = 'none';
            chatBody.style.display = 'block';
            searchInput.value = '';
            searchResults.innerHTML = '';
        }

        let searchTimeout;
        function rechercherMessages() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                const searchTerm = document.getElementById('searchInput').value.trim();
                const searchResults = document.getElementById('searchResults');

                if (searchTerm.length < 2) {
                    searchResults.innerHTML = '<div class="search-info">Tapez au moins 2 caractères pour rechercher...</div>';
                    return;
                }

                searchResults.innerHTML = '<div class="search-loading"><i class="fas fa-spinner fa-spin"></i> Recherche en cours...</div>';

                // Appel AJAX pour la recherche
                const currentUserId = document.getElementById('<%= hdnCurrentUserId.ClientID %>').value || '0';
                const conversationId = document.getElementById('<%= lblId.ClientID %>').innerText || '0';

                fetch('messagerie.aspx/RechercherMessages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        membreId: parseInt(currentUserId),
                        motCle: searchTerm,
                        conversationId: parseInt(conversationId)
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.d && data.d.length > 0) {
                        afficherResultatsRecherche(JSON.parse(data.d));
                    } else {
                        searchResults.innerHTML = '<div class="search-no-results">Aucun message trouvé pour "' + searchTerm + '"</div>';
                    }
                })
                .catch(error => {
                    console.error('Erreur de recherche:', error);
                    searchResults.innerHTML = '<div class="search-error">Erreur lors de la recherche</div>';
                });
            }, 300);
        }

        function afficherResultatsRecherche(resultats) {
            const searchResults = document.getElementById('searchResults');
            let html = '<div class="search-count">' + resultats.length + ' message(s) trouvé(s)</div>';

            resultats.forEach(function(message) {
                const date = new Date(message.DateEnvoi).toLocaleString('fr-FR');
                html += `
                    <div class="search-result-item">
                        <div class="search-result-header">
                            <strong>${message.Expediteur}</strong>
                            <span class="search-result-date">${date}</span>
                        </div>
                        <div class="search-result-content">${highlightSearchTerm(message.Contenu, document.getElementById('searchInput').value)}</div>
                    </div>
                `;
            });

            searchResults.innerHTML = html;
        }

        function highlightSearchTerm(text, searchTerm) {
            if (!searchTerm) return text;
            const regex = new RegExp('(' + searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
            return text.replace(regex, '<mark>$1</mark>');
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />
<asp:HiddenField ID="hdnPageToken" runat="server" />
<asp:HiddenField ID="hdnLastMessageHash" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">👥 Contacts</div>
                    <div class="contacts-search">
                        <input type="text" id="searchContacts" placeholder="Rechercher un contact...">
                        <button type="button" id="btnNewGroup" class="btn btn-sm btn-primary mt-2">
                            <i class="fas fa-plus"></i> Nouveau groupe
                        </button>
                    </div>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <div class="chat-header-info">
                            <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                            <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                            <span id="typingIndicator" class="typing-indicator" style="display: none;">
                                <i class="fas fa-circle"></i> En train d'écrire...
                            </span>
                        </div>
                        <div class="chat-header-actions">
                            <button type="button" id="btnSearchMessages" class="btn btn-sm btn-outline-secondary"
                                    onclick="toggleSearchBox()" title="Rechercher dans les messages">
                                <i class="fas fa-search"></i>
                            </button>
                            <button type="button" id="btnAttachFile" class="btn btn-sm btn-outline-secondary"
                                    onclick="document.getElementById('<%= fileUpload.ClientID %>').click()"
                                    title="Joindre un fichier">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button type="button" id="btnMoreOptions" class="btn btn-sm btn-outline-secondary"
                                    title="Plus d'options">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Zone de recherche (masquée par défaut) -->
                    <div id="searchContainer" class="search-container" style="display: none;">
                        <div class="search-input-group">
                            <input type="text" id="searchInput" placeholder="Rechercher dans les messages..."
                                   onkeyup="rechercherMessages()" class="form-control">
                            <button type="button" onclick="clearSearch()" class="btn btn-sm btn-secondary">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div id="searchResults" class="search-results"></div>
                    </div>

                    <div class="chat-body" id="chatBody">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <p><%# Eval("Contenu") %></p>

                <%-- Si le message contient une pièce jointe --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <a href='<%# Eval("AttachmentUrl") %>' target="_blank" class="attachment-link">
                        📎 Voir la pièce jointe
                    </a>
                </asp:Panel>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-footer">
                        <!-- Zone de prévisualisation des fichiers -->
                        <div id="filePreview" class="file-preview-container" style="display: none;"></div>

                        <!-- Zone de recherche de messages (masquée par défaut) -->
                        <div id="messageSearchContainer" class="message-search-container" style="display: none;">
                            <input type="text" id="searchMessages" placeholder="Rechercher dans les messages..." class="form-control">
                            <button type="button" id="btnCloseSearch" class="btn btn-sm btn-secondary">×</button>
                        </div>

                        <!-- Zone de saisie du message -->
                        <div class="message-input-container">
                            <asp:FileUpload ID="fileUpload" runat="server"
                                          accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar"
                                          style="display: none;"
                                          onchange="handleFileSelection(this)" />

                            <button type="button" id="btnEmoji" class="btn btn-sm btn-outline-secondary emoji-btn"
                                    onclick="toggleEmojiPicker()">
                                😊
                            </button>

                            <textarea rows="2" runat="server" id="txtMessage"
                                     placeholder="Écrivez votre message..."
                                     class="message-textarea"></textarea>

                            <button type="button" class="btn btn-sm btn-outline-secondary attach-btn"
                                    onclick="document.getElementById('<%= fileUpload.ClientID %>').click()"
                                    title="Joindre un fichier">
                                <i class="fas fa-paperclip"></i>
                            </button>

                            <asp:Button ID="btnEnvoyerAvecFichier" runat="server"
                                       Text="📎 Envoyer avec fichier"
                                       CssClass="btn btn-success send-file-btn"
                                       OnClick="btnEnvoyerAvecFichier_Click"
                                       style="display: none;" />

                            <button type="button" runat="server" id="btnenvoie"
                                    onserverclick="btnenvoie_ServerClick"
                                    class="btn btn-primary send-btn"
                                    onclick="return preventDuplicateSubmission();">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- Picker d'émojis (optionnel) -->
                        <div id="emojiPicker" class="emoji-picker" style="display: none;">
                            <div class="emoji-grid">
                                <span onclick="insertEmoji('😊')">😊</span>
                                <span onclick="insertEmoji('😂')">😂</span>
                                <span onclick="insertEmoji('❤️')">❤️</span>
                                <span onclick="insertEmoji('👍')">👍</span>
                                <span onclick="insertEmoji('👎')">👎</span>
                                <span onclick="insertEmoji('🎉')">🎉</span>
                                <span onclick="insertEmoji('🔥')">🔥</span>
                                <span onclick="insertEmoji('💯')">💯</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
        }

        .chat-wrapper {
            display: flex;
            height: 80vh;
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
            background: #fff;
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

            .chat-footer textarea {
                flex: 1;
                border-radius: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                resize: none;
            }

            .chat-footer button {
                background: #008374;
                color: #fff;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                font-weight: bold;
            }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

            .contacts-search input {
                width: 100%;
                padding: 8px;
                border-radius: 8px;
                border: 1px solid #ccc;
            }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

/* Nouveaux styles pour les fonctionnalités améliorées */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header-info {
    flex: 1;
}

.chat-header-actions {
    display: flex;
    gap: 5px;
}

.typing-indicator {
    font-size: 12px;
    color: #666;
    font-style: italic;
}

.typing-indicator i {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

.message-input-container {
    display: flex;
    align-items: flex-end;
    gap: 8px;
}

.message-textarea {
    flex: 1;
    border-radius: 20px;
    padding: 10px 15px;
    border: 1px solid #ddd;
    resize: none;
    max-height: 100px;
    min-height: 40px;
}

.emoji-btn, .attach-btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-preview-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
}

.file-preview-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.file-preview-item:last-child {
    border-bottom: none;
}

.file-name {
    font-weight: 500;
}

.file-size {
    color: #666;
    font-size: 12px;
}

.message-search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.emoji-picker {
    position: absolute;
    bottom: 60px;
    left: 10px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 5px;
}

.emoji-grid span {
    padding: 5px;
    cursor: pointer;
    border-radius: 4px;
    text-align: center;
    font-size: 18px;
}

.emoji-grid span:hover {
    background: #f0f0f0;
}

.notification-badge {
    background: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    position: absolute;
    top: 5px;
    right: 5px;
    min-width: 18px;
    text-align: center;
}

.contact-item {
    position: relative;
}

.contact-item.unread {
    background-color: #e8f5e8;
    font-weight: bold;
}

.contact-item.unread::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: #008374;
    border-radius: 50%;
}

/* Styles pour la recherche */
.search-container {
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.search-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-input-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 20px;
    outline: none;
}

.search-input-group input:focus {
    border-color: #008374;
    box-shadow: 0 0 0 2px rgba(0, 131, 116, 0.2);
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
}

.search-result-item {
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 8px;
    background: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background: #f0f8ff;
}

.search-result-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 12px;
}

.search-result-date {
    color: #666;
}

.search-result-content {
    font-size: 14px;
    line-height: 1.4;
}

.search-result-content mark {
    background: #ffeb3b;
    padding: 1px 2px;
    border-radius: 2px;
}

.search-info, .search-loading, .search-no-results, .search-error {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.search-loading {
    color: #008374;
}

.search-error {
    color: #dc3545;
}

.search-count {
    font-weight: bold;
    margin-bottom: 10px;
    color: #008374;
}

/* Styles pour l'upload de fichiers */
.send-file-btn {
    border-radius: 20px !important;
    padding: 8px 16px !important;
    font-size: 12px !important;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-info i {
    color: #008374;
}

/* Responsive pour mobile */
@media (max-width: 768px) {
    .search-container {
        padding: 10px;
    }

    .search-input-group {
        flex-direction: column;
    }

    .search-result-header {
        flex-direction: column;
        gap: 5px;
    }

    .send-file-btn {
        font-size: 10px !important;
        padding: 6px 12px !important;
    }
}

    </style>


</asp:Content>
