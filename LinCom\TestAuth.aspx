<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TestAuth.aspx.cs" Inherits="LinCom.TestAuth" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Test Authentification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .info-box { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>🔐 Test d'Authentification LinCom</h1>
            
            <div class="info-box warning">
                <h3>📋 Instructions</h3>
                <p>Cette page teste l'authentification avant d'accéder à la messagerie.</p>
                <ol>
                    <li>Vérifiez vos cookies d'authentification</li>
                    <li>Créez une session de test si nécessaire</li>
                    <li>Accédez à la messagerie</li>
                </ol>
            </div>
            
            <div class="info-box">
                <h3>📊 État de l'Authentification</h3>
                <asp:Label ID="lblAuthStatus" runat="server" Text="Vérification en cours..."></asp:Label>
            </div>
            
            <div class="info-box">
                <h3>🍪 Cookies Détectés</h3>
                <asp:Label ID="lblCookies" runat="server" Text="Analyse des cookies..."></asp:Label>
            </div>
            
            <div class="info-box">
                <h3>🔧 Actions Disponibles</h3>
                <asp:Button ID="btnCreateTestSession" runat="server" Text="Créer Session Test" 
                           CssClass="btn btn-primary" OnClick="btnCreateTestSession_Click" />
                <asp:Button ID="btnGoToMessagerie" runat="server" Text="Aller à la Messagerie" 
                           CssClass="btn btn-success" OnClick="btnGoToMessagerie_Click" />
                <asp:Button ID="btnClearCookies" runat="server" Text="Effacer Cookies" 
                           CssClass="btn" OnClick="btnClearCookies_Click" 
                           style="background-color: #dc3545; color: white;" />
            </div>
            
            <div class="info-box">
                <h3>📝 Log des Actions</h3>
                <asp:Label ID="lblLog" runat="server" Text="Prêt pour les tests..."></asp:Label>
            </div>
        </div>
    </form>
</body>
</html>
