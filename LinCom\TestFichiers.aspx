<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TestFichiers.aspx.cs" Inherits="LinCom.TestFichiers" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test Accès Fichiers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .file-item { padding: 10px; margin: 5px 0; border: 1px solid #eee; border-radius: 3px; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>🔧 Test d'Accès aux Fichiers de Messagerie</h1>
        
        <div class="test-section info">
            <h3>📁 Informations sur le Dossier</h3>
            <asp:Literal ID="litInfoDossier" runat="server"></asp:Literal>
        </div>
        
        <div class="test-section">
            <h3>📋 Test d'Upload</h3>
            <asp:FileUpload ID="testFileUpload" runat="server" />
            <asp:Button ID="btnTestUpload" runat="server" Text="Tester Upload" OnClick="btnTestUpload_Click" />
        </div>
        
        <div class="test-section">
            <h3>📂 Fichiers Existants</h3>
            <asp:Literal ID="litFichiersExistants" runat="server"></asp:Literal>
        </div>
        
        <div class="test-section">
            <h3>🔗 Test d'Accès URL</h3>
            <asp:Literal ID="litTestURL" runat="server"></asp:Literal>
        </div>
    </form>
</body>
</html>
