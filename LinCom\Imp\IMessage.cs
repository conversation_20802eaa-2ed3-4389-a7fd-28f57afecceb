﻿using LinCom.Class;
using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMessage
    {
        void AfficherDetails(long messageId, Message_Class message);
        int Envoyer(Message_Class message);
        int Modifier(Message_Class message);
        int Supprimer(long messageId);
        void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages = 50);
        int CompterNonLus(long membreId);

        //Interface pour Message Statut
        void AfficherDetailsMessageStatut(long statusId, MessageStatus_Class statusClass);
        int MarquerCommeLu(long messageId, long userId);
        int AjouterMessageEtStatusPourTous(long conversationId, long senderId, string contenu, string attachmentUrl = null);
        int SupprimerMessageStatut(long messageStatusId);
        int EnvoyerMessageStatus(MessageStatus_Class messageClass);

        // Nouvelles méthodes pour améliorer la messagerie
        void RechercherMessages(Repeater rpt, long membreId, string motCle);
        void ChargerConversationsRecentes(Repeater rpt, long membreId, int limite = 10);
        int CompterMessagesNonLusConversation(long conversationId, long membreId);
        void MarquerConversationCommeLue(long conversationId, long membreId);
        bool VerifierPermissionMessage(long messageId, long membreId);
        void ChargerMessagesAvecPagination(Repeater rpt, long conversationId, int page, int taille = 20);
    }
}
