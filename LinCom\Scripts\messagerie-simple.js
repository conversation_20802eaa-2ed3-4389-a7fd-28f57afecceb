// Script simple pour la messagerie LinCom
// Fonctionnalités : Upload de fichiers et recherche

// Variables globales (vérifier si elles existent déjà)
if (typeof isSubmitting === 'undefined') {
    var isSubmitting = false;
}
if (typeof selectedFile === 'undefined') {
    var selectedFile = null;
}

// Fonction pour sélectionner un fichier
function selectFile() {
    document.getElementById('fileInput').click();
}

// Fonction pour gérer la sélection de fichier
function handleFileSelect(input) {
    const file = input.files[0];
    if (file) {
        selectedFile = file;
        showFilePreview(file);
        
        // Afficher le bouton d'envoi avec fichier
        const btnSendFile = document.getElementById('btnSendFile');
        if (btnSendFile) {
            btnSendFile.style.display = 'inline-block';
        }
    }
}

// Fonction pour afficher la prévisualisation du fichier
function showFilePreview(file) {
    const preview = document.getElementById('filePreview');
    const fileName = file.name;
    const fileSize = formatFileSize(file.size);
    const fileType = getFileIcon(file.type);
    
    preview.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: space-between; padding: 10px; background: #e9ecef; border-radius: 8px;">
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 20px;">${fileType}</span>
                <div>
                    <div style="font-weight: bold;">${fileName}</div>
                    <div style="font-size: 12px; color: #666;">${fileSize}</div>
                </div>
            </div>
            <button type="button" onclick="removeFile()" 
                    style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 16px;">
                ×
            </button>
        </div>
    `;
    preview.style.display = 'block';
}

// Fonction pour supprimer le fichier sélectionné
function removeFile() {
    document.getElementById('fileInput').value = '';
    document.getElementById('filePreview').style.display = 'none';
    
    const btnSendFile = document.getElementById('btnSendFile');
    if (btnSendFile) {
        btnSendFile.style.display = 'none';
    }
    
    selectedFile = null;
}

// Fonction pour formater la taille du fichier
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// Fonction pour obtenir l'icône du fichier selon son type
function getFileIcon(fileType) {
    if (fileType.startsWith('image/')) return '🖼️';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word') || fileType.includes('document')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📈';
    if (fileType.includes('text')) return '📃';
    if (fileType.includes('zip') || fileType.includes('rar')) return '🗜️';
    return '📎';
}

// Fonction de recherche
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    const resultsDiv = document.getElementById('searchResults');
    
    if (searchTerm.length < 2) {
        resultsDiv.style.display = 'none';
        return;
    }
    
    // Afficher un indicateur de chargement
    resultsDiv.innerHTML = '<div class="search-item">🔍 Recherche en cours...</div>';
    resultsDiv.style.display = 'block';
    
    // Simuler une recherche (remplacer par un vrai appel AJAX)
    setTimeout(() => {
        const mockResults = [
            { expediteur: 'John Doe', contenu: `Message contenant "${searchTerm}"`, date: '2024-01-15' },
            { expediteur: 'Jane Smith', contenu: `Autre message avec "${searchTerm}"`, date: '2024-01-14' }
        ];
        
        showSearchResults(mockResults);
    }, 500);
}

// Fonction pour afficher les résultats de recherche
function showSearchResults(results) {
    const resultsDiv = document.getElementById('searchResults');
    resultsDiv.innerHTML = '';
    
    if (results.length > 0) {
        results.forEach(result => {
            const item = document.createElement('div');
            item.className = 'search-item';
            item.innerHTML = `
                <div style="font-weight: bold;">${result.expediteur}</div>
                <div style="font-size: 14px; color: #666;">${result.contenu}</div>
                <div style="font-size: 12px; color: #999;">${result.date}</div>
            `;
            item.onclick = () => selectSearchResult(result);
            resultsDiv.appendChild(item);
        });
        resultsDiv.style.display = 'block';
    } else {
        resultsDiv.innerHTML = '<div class="search-item">❌ Aucun résultat trouvé</div>';
        resultsDiv.style.display = 'block';
    }
}

// Fonction pour sélectionner un résultat de recherche
function selectSearchResult(result) {
    document.getElementById('searchInput').value = '';
    document.getElementById('searchResults').style.display = 'none';
    
    // Ici, vous pouvez ajouter la logique pour naviguer vers le message
    alert(`Résultat sélectionné: ${result.contenu}`);
}

// Fonction pour empêcher la duplication d'envoi
function preventDuplicate() {
    if (isSubmitting) {
        alert('⏳ Envoi en cours, veuillez patienter...');
        return false;
    }
    
    isSubmitting = true;
    
    // Réactiver après 3 secondes
    setTimeout(() => {
        isSubmitting = false;
    }, 3000);
    
    return true;
}

// Fonction pour valider l'envoi de fichier
function validateFileUpload() {
    if (!selectedFile) {
        alert('❌ Aucun fichier sélectionné');
        return false;
    }
    
    // Vérifier la taille du fichier (max 10 MB)
    const maxSize = 10 * 1024 * 1024; // 10 MB
    if (selectedFile.size > maxSize) {
        alert('❌ Le fichier est trop volumineux (max 10 MB)');
        return false;
    }
    
    // Vérifier le type de fichier
    const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
        'application/pdf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
    ];
    
    if (!allowedTypes.includes(selectedFile.type)) {
        alert('❌ Type de fichier non autorisé');
        return false;
    }
    
    return preventDuplicate();
}

// Fonction pour masquer les résultats de recherche quand on clique ailleurs
document.addEventListener('click', function(event) {
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    
    if (searchInput && searchResults) {
        if (!searchInput.contains(event.target) && !searchResults.contains(event.target)) {
            searchResults.style.display = 'none';
        }
    }
});

// Fonction pour gérer la touche Entrée dans la zone de message
function handleMessageKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        
        const btnEnvoie = document.getElementById('btnenvoie');
        if (btnEnvoie && !isSubmitting) {
            btnEnvoie.click();
        }
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter l'événement keypress au textarea de message
    const txtMessage = document.getElementById('txtMessage');
    if (txtMessage) {
        txtMessage.addEventListener('keypress', handleMessageKeyPress);
    }
    
    // Réinitialiser l'état
    isSubmitting = false;
    selectedFile = null;
    
    console.log('✅ Messagerie LinCom initialisée');
});

// Fonction utilitaire pour afficher des notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 9999;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    
    switch(type) {
        case 'success':
            notification.style.backgroundColor = '#28a745';
            break;
        case 'error':
            notification.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ffc107';
            notification.style.color = '#000';
            break;
        default:
            notification.style.backgroundColor = '#17a2b8';
    }
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Supprimer après 3 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Export des fonctions pour utilisation globale
window.selectFile = selectFile;
window.handleFileSelect = handleFileSelect;
window.removeFile = removeFile;
window.performSearch = performSearch;
window.preventDuplicate = preventDuplicate;
window.validateFileUpload = validateFileUpload;
window.showNotification = showNotification;
