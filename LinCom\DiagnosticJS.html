<!DOCTYPE html>
<html>
<head>
    <title>Diagnostic JavaScript - Messagerie LinCom</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .log-area { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic JavaScript - Messagerie LinCom</h1>
        
        <div class="test-section info">
            <h3>📋 Tests Automatiques</h3>
            <p>Cette page teste les fonctions JavaScript de la messagerie pour identifier les problèmes.</p>
            <button class="btn btn-primary" onclick="runAllTests()">🚀 Lancer Tous les Tests</button>
            <button class="btn btn-warning" onclick="clearLog()">🗑️ Vider Log</button>
        </div>
        
        <div class="test-section">
            <h3>🧪 Tests Individuels</h3>
            <button class="btn btn-success" onclick="testSelectFile()">📎 Test Sélection Fichier</button>
            <button class="btn btn-success" onclick="testFileValidation()">✅ Test Validation Fichier</button>
            <button class="btn btn-success" onclick="testSearch()">🔍 Test Recherche</button>
            <button class="btn btn-success" onclick="testAntiDuplicate()">🛡️ Test Anti-Doublon</button>
        </div>
        
        <div class="test-section">
            <h3>📊 Résultats des Tests</h3>
            <div id="testResults" class="log-area">Prêt pour les tests...</div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Simulation des Éléments de la Messagerie</h3>
            <!-- Éléments simulés pour les tests -->
            <input type="file" id="fileInput" style="display: none;" accept="image/*,.pdf,.doc,.docx,.txt" />
            <div id="filePreview" style="display: none; padding: 10px; background: #e9ecef; border-radius: 5px; margin: 10px 0;"></div>
            <input type="text" id="searchInput" placeholder="Test de recherche..." style="width: 100%; padding: 10px; margin: 10px 0;" />
            <div id="searchResults" style="display: none; border: 1px solid #ddd; border-radius: 5px; max-height: 100px; overflow-y: auto;"></div>
            <button id="btnSendFile" style="display: none; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px;">Envoyer Fichier</button>
        </div>
        
        <div class="test-section">
            <h3>🔗 Liens Utiles</h3>
            <a href="messagerie.aspx" class="btn btn-primary">💬 Aller à la Messagerie</a>
            <a href="TestMessagerie.aspx" class="btn btn-warning">🧪 Tests Complets</a>
        </div>
    </div>

    <script>
        // Variables globales pour les tests
        let testResults = [];
        let isSubmitting = false;
        let selectedFile = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107',
                'info': '#17a2b8'
            };
            
            const logArea = document.getElementById('testResults');
            const color = colors[type] || colors.info;
            
            logArea.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testResults').innerHTML = 'Log vidé - Prêt pour nouveaux tests...';
            testResults = [];
        }

        function runAllTests() {
            log('🚀 Début des tests automatiques...', 'info');
            clearLog();
            
            setTimeout(() => testSelectFile(), 100);
            setTimeout(() => testFileValidation(), 500);
            setTimeout(() => testSearch(), 1000);
            setTimeout(() => testAntiDuplicate(), 1500);
            setTimeout(() => showFinalResults(), 2000);
        }

        function testSelectFile() {
            log('📎 Test: Fonction de sélection de fichier...', 'info');
            
            try {
                // Test si la fonction existe
                if (typeof selectFile === 'function') {
                    log('✅ Fonction selectFile() trouvée', 'success');
                    testResults.push({name: 'selectFile', status: 'success'});
                } else {
                    log('❌ Fonction selectFile() non trouvée', 'error');
                    testResults.push({name: 'selectFile', status: 'error'});
                }
                
                // Test de l'élément fileInput
                const fileInput = document.getElementById('fileInput');
                if (fileInput) {
                    log('✅ Élément fileInput trouvé', 'success');
                } else {
                    log('❌ Élément fileInput non trouvé', 'error');
                }
                
            } catch (error) {
                log(`❌ Erreur lors du test selectFile: ${error.message}`, 'error');
                testResults.push({name: 'selectFile', status: 'error'});
            }
        }

        function testFileValidation() {
            log('✅ Test: Validation des fichiers...', 'info');
            
            try {
                // Test si la fonction existe
                if (typeof validateFileUpload === 'function') {
                    log('✅ Fonction validateFileUpload() trouvée', 'success');
                    
                    // Test avec aucun fichier
                    const result = validateFileUpload();
                    if (result === false) {
                        log('✅ Validation correcte: aucun fichier sélectionné', 'success');
                        testResults.push({name: 'validateFileUpload', status: 'success'});
                    } else {
                        log('⚠️ Validation inattendue avec aucun fichier', 'warning');
                        testResults.push({name: 'validateFileUpload', status: 'warning'});
                    }
                } else {
                    log('❌ Fonction validateFileUpload() non trouvée', 'error');
                    testResults.push({name: 'validateFileUpload', status: 'error'});
                }
                
            } catch (error) {
                log(`❌ Erreur lors du test validateFileUpload: ${error.message}`, 'error');
                testResults.push({name: 'validateFileUpload', status: 'error'});
            }
        }

        function testSearch() {
            log('🔍 Test: Fonction de recherche...', 'info');
            
            try {
                // Test si la fonction existe
                if (typeof performSearch === 'function') {
                    log('✅ Fonction performSearch() trouvée', 'success');
                    testResults.push({name: 'performSearch', status: 'success'});
                } else {
                    log('❌ Fonction performSearch() non trouvée', 'error');
                    testResults.push({name: 'performSearch', status: 'error'});
                }
                
                // Test des éléments de recherche
                const searchInput = document.getElementById('searchInput');
                const searchResults = document.getElementById('searchResults');
                
                if (searchInput && searchResults) {
                    log('✅ Éléments de recherche trouvés', 'success');
                } else {
                    log('❌ Éléments de recherche manquants', 'error');
                }
                
            } catch (error) {
                log(`❌ Erreur lors du test de recherche: ${error.message}`, 'error');
                testResults.push({name: 'performSearch', status: 'error'});
            }
        }

        function testAntiDuplicate() {
            log('🛡️ Test: Protection anti-doublon...', 'info');
            
            try {
                // Test si la fonction existe
                if (typeof preventDuplicate === 'function') {
                    log('✅ Fonction preventDuplicate() trouvée', 'success');
                    
                    // Test du mécanisme
                    isSubmitting = false;
                    const result1 = preventDuplicate();
                    const result2 = preventDuplicate();
                    
                    if (result1 === true && result2 === false) {
                        log('✅ Protection anti-doublon fonctionne', 'success');
                        testResults.push({name: 'preventDuplicate', status: 'success'});
                    } else {
                        log('⚠️ Protection anti-doublon inattendue', 'warning');
                        testResults.push({name: 'preventDuplicate', status: 'warning'});
                    }
                    
                    // Réinitialiser
                    isSubmitting = false;
                } else {
                    log('❌ Fonction preventDuplicate() non trouvée', 'error');
                    testResults.push({name: 'preventDuplicate', status: 'error'});
                }
                
            } catch (error) {
                log(`❌ Erreur lors du test anti-doublon: ${error.message}`, 'error');
                testResults.push({name: 'preventDuplicate', status: 'error'});
            }
        }

        function showFinalResults() {
            log('📊 Résumé des tests:', 'info');
            
            const successCount = testResults.filter(t => t.status === 'success').length;
            const errorCount = testResults.filter(t => t.status === 'error').length;
            const warningCount = testResults.filter(t => t.status === 'warning').length;
            
            log(`✅ Succès: ${successCount}`, 'success');
            log(`❌ Erreurs: ${errorCount}`, 'error');
            log(`⚠️ Avertissements: ${warningCount}`, 'warning');
            
            if (errorCount === 0) {
                log('🎉 Tous les tests principaux sont réussis !', 'success');
                log('💬 Vous pouvez utiliser la messagerie en toute sécurité.', 'success');
            } else {
                log('⚠️ Certaines fonctions ont des problèmes.', 'warning');
                log('🔧 Vérifiez le chargement des scripts JavaScript.', 'warning');
            }
        }

        // Fonctions de base pour les tests (fallback)
        function selectFile() {
            document.getElementById('fileInput').click();
        }

        function validateFileUpload() {
            const fileInput = document.getElementById('fileInput');
            if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                return false;
            }
            return true;
        }

        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            return searchTerm.length >= 2;
        }

        function preventDuplicate() {
            if (isSubmitting) {
                return false;
            }
            isSubmitting = true;
            setTimeout(() => { isSubmitting = false; }, 2000);
            return true;
        }

        // Initialisation
        window.onload = function() {
            log('🔍 Diagnostic JavaScript initialisé', 'info');
            log('📝 Cliquez sur "Lancer Tous les Tests" pour commencer', 'info');
        };
    </script>
</body>
</html>
